# 🌐 Nazwa Projektu

Frontendowa aplikacja zbudowana z użyciem [Next.js](https://nextjs.org/) oraz [Tailwind CSS](https://tailwindcss.com/), z komponentami UI dostarczanymi przez [shadcn/ui](https://ui.shadcn.com/).

## 🧰 Technologie

- [Next.js 15+](https://nextjs.org/)
- [React 18+](https://reactjs.org/)
- [Tailwind CSS](https://tailwindcss.com/)
- [shadcn/ui](https://ui.shadcn.com/)
- TypeScript
- PostCSS
- Radix UI (jako baza komponentów shadcn/ui)

## 📦 Instalacja

```bash
git clone https://gitlab.devforyou.pl/avotech/page_dashboard
cd page_dashboard
yarn
```

Wybierz ścieżkę np. `components/ui` oraz preferowany sposób stylowania (`tailwind` wymagany).

Aby dodać komponent:

```bash
npx shadcn-ui@latest add button
```

## 🚀 Uruchomienie projektu

```bash
yarn dev
```

Aplikacja będzie dostępna pod adresem: [http://localhost:3000](http://localhost:3000)

## 🗂 Struktura katalogów

```
.
├── src/               
│   └── app/           # App Router i strony (Next.js 13+)
│   └── components/    # Komponenty ogólne i UI
│   └── lib/           # Pomocnicze funkcje, hooki, utils
├── styles/            # Pliki stylów globalnych
├── public/            # Zasoby publiczne
├── tailwind.config.ts# Konfiguracja Tailwinda
├── postcss.config.js  # Konfiguracja PostCSS
├── next.config.js     # Konfiguracja Next.js
└── tsconfig.json      # Konfiguracja TypeScript
```

## 🧪 Skrypty

| Komenda           | Opis                                  |
|-------------------|----------------------------------------|
| `yarn dev`     | Uruchamia projekt w trybie developerskim |
| `yarn build`   | Buduje aplikację do produkcji         |
| `yarn start`   | Uruchamia zbudowaną aplikację         |
| `yarn lint`    | Uruchamia ESLint                      |

## ✅ Checklista

- [x] Next.js z App Routerem
- [x] Tailwind CSS z prekonfiguracją
- [x] shadcn/ui z dodanym przykładowym komponentem (`button`)
- [ ] Konfiguracja SEO / metadata
- [ ] Responsywny layout

## 👨‍💻 Autorzy

- [Krzysztof Włodarski](https://github.com/ziut3k-dev)
