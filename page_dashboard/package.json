{"name": "my-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "next": "15.4.3", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1", "three": "^0.178.0"}, "devDependencies": {"@eslint/eslintrc": "3.3.1", "@tailwindcss/postcss": "4.1.11", "@types/node": "20.6.0", "@types/react": "19.0.12", "@types/react-dom": "19.0.4", "eslint": "9.31.0", "eslint-config-next": "15.4.3", "tailwindcss": "4.1.11", "tw-animate-css": "^1.3.5", "typescript": "5.8.3"}}