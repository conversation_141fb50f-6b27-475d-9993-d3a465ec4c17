const express = require('express');
const router = express.Router();

module.exports = (gyroService) => {
    router.get('/', (req, res) => {
        gyroService.getGyroData(req, res);
    });

    router.get('/orientation', (req, res) => {
        gyroService.getOrientation(req, res);
    });

    router.get('/motion-status', (req, res) => {
        gyroService.getMotionStatus(req, res);
    });

    // Dodana nowa trasa diagnostyczna
    router.get('/status', (req, res) => {
        gyroService.getSensorStatus(req, res);
    });

    return router;
};