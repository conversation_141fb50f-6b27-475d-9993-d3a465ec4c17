const express = require('express');
const { log } = require('../utils/logger');

module.exports = (temperatureService) => {
  const router = express.Router();

  /**
   * @api {get} /api/temperatures Pobierz aktualne temperatury
   * @apiName GetTemperatures
   * @apiGroup Temperature
   */
  router.get('/', (req, res) => {
    try {
      const readings = temperatureService.getReadings();
      log.info(`Zwrócono odczyty temperatur: ${JSON.stringify(readings)}`);
      res.json({
        success: true,
        data: readings
      });
    } catch (err) {
      log.error(`Błąd pobierania temperatur: ${err.message}`);
      res.status(500).json({
        success: false,
        error: err.message
      });
    }
  });

  return router;
};