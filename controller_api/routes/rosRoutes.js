const express = require('express');
const { log } = require('../utils/logger');

module.exports = (rosService) => {
  const router = express.Router();

  router.get('/status', (req, res) => {
    try {
      const node = rosService.node;
      if (!node) throw new Error('ROS node not available');
      
      res.json({
        status: 'active',
        topics: node.getTopicNamesAndTypes()
      });
    } catch (err) {
      log.error(err.message);
      res.status(500).json({ error: err.message });
    }
  });

  router.post('/publish', (req, res) => {
    try {
      const { topic, message } = req.body;
      const publisher = rosService.createPublisher('std_msgs/msg/String', topic);
      publisher.publish({ data: message });
      
      res.json({ 
        success: true,
        message: `Published to ${topic}`
      });
    } catch (err) {
      log.error(err.message);
      res.status(500).json({ error: err.message });
    }
  });

  return router;
};