const { log } = require('../utils/logger');

class TemperatureService {
  constructor(rosService) {
    this.rosService = rosService;
    this.readings = {
      temperature_1: null,
      temperature_2: null,
      lastUpdated: null
    };
  }

  init() {
    // Subskrypcja tematów temperatury
    this.rosService.createSubscriber(
      'std_msgs/msg/Float32',
      '/ads/temperature_1',
      (msg) => {
        this.readings.temperature_1 = msg.data;
        this.readings.lastUpdated = new Date().toISOString();
        // log.ros(`Odebrano temperature_1: ${msg.data}°C`);
      }
    );

    this.rosService.createSubscriber(
      'std_msgs/msg/Float32',
      '/ads/temperature_2',
      (msg) => {
        this.readings.temperature_2 = msg.data;
        this.readings.lastUpdated = new Date().toISOString();
        // log.ros(`Odebrano temperature_2: ${msg.data}°C`);
      }
    );

    log.success('Zainicjalizowano subskrypcje temperatur');
  }

  getReadings() {
    return {
      ...this.readings,
      timestamp: new Date().toISOString()
    };
  }
}

module.exports = TemperatureService;