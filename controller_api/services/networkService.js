const os = require('os');

function getLocalNetworkIps() {
    const interfaces = os.networkInterfaces();
    const results = { ipv4: [], ipv6: [] };

    for (const interfaceName in interfaces) {
        for (const iface of interfaces[interfaceName]) {
            if (!iface.internal) {
                if (iface.family === 'IPv4') {
                    results.ipv4.push({
                        interface: interfaceName,
                        address: iface.address
                    });
                } else if (iface.family === 'IPv6') {
                    results.ipv6.push({
                        interface: interfaceName,
                        address: iface.address
                    });
                }
            }
        }
    }
    return results;
}

function getClientIp(req) {
    return req.headers['x-forwarded-for']?.split(',')[0] ||
        req.connection?.remoteAddress ||
        req.socket?.remoteAddress ||
        req.connection?.socket?.remoteAddress;
}

module.exports = {
    getLocalNetworkIps,
    getClientIp
};