const rclnodejs = require('rclnodejs');
const { log } = require('../utils/logger');

class RosService {
    constructor() {
        this.node = null;
        this.subscribers = {};
        this.publishers = {};
        this.remoteNodes = new Map();
    }

    async init() {
        if (this.node) return this.node;

        try {
            await rclnodejs.init();
            this.node = new rclnodejs.Node('controller_server');
            log.success('ROS node initialized');
            return this.node;
        } catch (err) {
            log.error(`ROS init failed: ${err.message}`);
            throw err;
        }
    }
    setupRemoteMonitoring() {
        // Monitorowanie dostępności węzłów
        this.node.createSubscription(
            'rcl_interfaces/msg/ParameterEvent',
            '/parameter_events',
            (msg) => {
                // if (msg.node.startsWith('temperature_node_')) {
                    this.remoteNodes.set(msg.node, {
                        lastSeen: Date.now(),
                        ip: msg.ip_address // Zak<PERSON>, że IP jest przekazywane w parametrach
                    });
                    log.info(`Zdalny węzeł ${msg.node} aktywny`);
                // }
            }
        )
    }
    start() {
        if (!this.node) throw new Error('Node not initialized');
        this.node.spin();
        this.setupRemoteMonitoring();
        console.log(this.remoteNodes)
        log.info('ROS node spinning');
    }

    createPublisher(msgType, topic) {
        if (!this.publishers[topic]) {
            this.publishers[topic] = this.node.createPublisher(msgType, topic);
            log.info(`Created publisher for ${topic}`);
        }
        return this.publishers[topic];
    }

    createSubscriber(msgType, topic, callback) {
        if (!this.subscribers[topic]) {
            this.subscribers[topic] = this.node.createSubscription(msgType, topic, callback);
            log.info(`Created subscriber for ${topic}`);
        }
        return this.subscribers[topic];
    }

    async shutdown() {
        if (this.node) {
            await this.node.destroy();
            this.node = null;
            log.info('ROS node shutdown');
        }
    }
}

module.exports = new RosService();