const { log } = require('../utils/logger');

class PowerService {
  constructor(rosService) {
    this.rosService = rosService;
    this.readings = {
      voltage: null,
      current: null,
      power: null,
      shunt_voltage: null,
      lastUpdated: null
    };
  }

  init() {
    // Subskrypcja tematu z danymi zasilania
    this.rosService.createSubscriber(
      'std_msgs/msg/Float32MultiArray',
      '/power/power_data',
      (msg) => {
        // Przypisanie danych z tablicy
        // msg.data = [voltage, current, power, shunt_voltage]
        this.readings.voltage = msg.data[0];
        this.readings.current = msg.data[1];
        this.readings.power = msg.data[2];
        this.readings.shunt_voltage = msg.data[3];
        this.readings.lastUpdated = new Date().toISOString();

        // log.ros(`Odebrano dane zasilania: ${msg.data[0].toFixed(2)}V, ${msg.data[1].toFixed(2)}A`);
      }
    );

    log.success('Zainicjalizowano subskrypcję danych zasilania');
  }

  getReadings() {
    return {
      ...this.readings,
      timestamp: new Date().toISOString()
    };
  }

  // Dodatkowe metody dla tras (routes)
  getPowerData(req, res) {
    const data = this.getReadings();
    res.json({
      success: true,
      data: {
        voltage: data.voltage,
        current: data.current,
        power: data.power,
        updatedAt: data.lastUpdated
      }
    });
  }

  getPowerSummary(req, res) {
    const data = this.getReadings();
    res.json({
      success: true,
      data: {
        voltage: {
          value: data.voltage,
          unit: 'V'
        },
        current: {
          value: data.current,
          unit: 'A'
        },
        power: {
          value: data.power,
          unit: 'W'
        },
        status: this.getBatteryStatus(data.voltage),
        updatedAt: data.lastUpdated
      }
    });
  }

  getBatteryStatus(voltage) {
    // Prosta logika określająca stan baterii 3S LiPo
    if (!voltage) return 'unknown';
    if (voltage >= 12.6) return 'fully_charged';
    if (voltage >= 11.1) return 'good';
    if (voltage >= 10.5) return 'low';
    return 'critical';
  }
}

module.exports = PowerService;