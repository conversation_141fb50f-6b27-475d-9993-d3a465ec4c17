const i2c = require('i2c-bus');
const { log } = require('../utils/logger');

class Ads7830Service {
  constructor(rosService = null, i2cBusNumber = 1, address = 0x48) {
    this.i2cBusNumber = i2cBusNumber;
    this.address = address;
    this.rosService = rosService;
    this.readings = [null, null, null, null]; // AIN0, AIN1, AIN4, AIN5
    this.lastUpdated = null;
    this.joystickPublisher = null;
    this.pollingInterval = null;
  }

  async init() {
    try {
      this.bus = await i2c.openPromisified(this.i2cBusNumber);
      log.success(`ADS7830 initialized at 0x${this.address.toString(16)}`);

      if (this.rosService) {
        this.joystickPublisher = this.rosService.createPublisher(
          'std_msgs/msg/Float32MultiArray',
          '/controller/joystick'
        );
      }

      this.startPolling(50); // np. 100ms
    } catch (err) {
      log.error(`ADS7830 init failed: ${err.message}`);
      throw err;
    }
  }

  async readChannel(channel, methodByte = 0x84) {
    const controlByte = methodByte | ((channel & 0x07) << 4);
    try {
      const writeBuf = Buffer.from([controlByte]);
      const readBuf = Buffer.alloc(1);

      await this.bus.i2cWrite(this.address, 1, writeBuf);
      await new Promise(r => setTimeout(r, 1));
      await this.bus.i2cRead(this.address, 1, readBuf);

      return readBuf[0];
    } catch (err) {
      log.warn(`Read fail ch=${channel}: ${err.message}`);
      return null;
    }
  }

  normalize(value) {
    return (value !== null ? (value / 127.5 - 1.0) : 0.0).toFixed(1);
  }

  async updateReadings() {
    try {
      // Kolejność: AIN0, AIN1, AIN4, AIN5
      const channelMap = [0, 4, 1, 5];
      const methodByte = 0x84;

      const raw = [];
      for (const ch of channelMap) {
        const val = await this.readChannel(ch, methodByte);
        raw.push(val);
      }

      const joy1X = this.normalize(raw[0]) * -1;  // Odwrócony X dla Joystick 1
      const joy1Y = this.normalize(raw[1]) * 1;
      const joy2X = this.normalize(raw[2]) * -1;  // Odwrócony X dla Joystick 2
      const joy2Y = this.normalize(raw[3]) * 1;
      this.readings = [joy1X, joy1Y, joy2X, joy2Y];
      this.lastUpdated = new Date();

      // Konsola: ładny log z wartościami
      // log.info(`🎮 Joystick 1 (X/Y): ${joy1X}, ${joy1Y} | Joystick 2 (X/Y): ${joy2X}, ${joy2Y}`);

      // ROS publish
      if (this.joystickPublisher) {
        this.joystickPublisher.publish({ data: this.readings });
      }

    } catch (err) {
      log.error(`ADS7830 update failed: ${err.message}`);
    }
  }

  startPolling(intervalMs = 100) {
    if (this.pollingInterval) clearInterval(this.pollingInterval);
    this.pollingInterval = setInterval(() => this.updateReadings(), intervalMs);
    log.info(`🔄 Polling ADS7830 co ${intervalMs} ms`);
  }

  getReadings() {
    return {
      joystick1: { x: this.readings[0], y: this.readings[1] },
      joystick2: { x: this.readings[2], y: this.readings[3] },
      lastUpdated: this.lastUpdated
    };
  }

  getAnalogData(req, res) {
    res.json({ success: true, data: this.getReadings() });
  }

  getChannelValue(req, res) {
    const channel = parseInt(req.params.channel);
    if (![0, 1, 2, 3].includes(channel)) {
      return res.status(400).json({
        success: false,
        message: 'Kanał musi być w zakresie 0-3 (Joystick1 X/Y, Joystick2 X/Y)'
      });
    }

    res.json({
      success: true,
      data: {
        channel,
        value: this.readings[channel],
        lastUpdated: this.lastUpdated
      }
    });
  }
}

module.exports = Ads7830Service;
