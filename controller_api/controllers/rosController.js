const {
    createPublisher,
    createSubscriber,
    getActiveTopics
} = require('../services/rosService');

class RosController {
    constructor() {
        this.publishers = {};
        this.subscribers = {};
    }

    initPublishers(topics) {
        topics.forEach(topic => {
            this.publishers[topic.name] = createPublisher(topic.type, topic.name);
        });
    }

    initSubscribers(topics) {
        topics.forEach(topic => {
            this.subscribers[topic.name] = createSubscriber(
                topic.type,
                topic.name,
                topic.callback
            );
        });
    }

    getStatus() {
        return {
            publishers: Object.keys(this.publishers),
            subscribers: Object.keys(this.subscribers),
            activeTopics: getActiveTopics()
        };
    }
}

module.exports = new RosController();