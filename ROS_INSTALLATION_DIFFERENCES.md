# Różnice w instalacji ROS2 - ARGUS vs Standardowa

## 📍 Mapowanie ścieżek: Standardowa → ARGUS

### Główne katalogi instalacyjne:
- **GŁÓWNY KATALOG INSTALACYJNY:** `/opt/ros/jazzy/` → `/root/ros2_jazzy/`
- **SKRYPT SETUP:** `/opt/ros/jazzy/setup.bash` → `/root/ros2_jazzy/install/setup.bash`
- **PLIKI WYKONYWALNE:** `/opt/ros/jazzy/bin/` → `/root/ros2_jazzy/install/bin/`
- **BIBLIOTEKI:** `/opt/ros/jazzy/lib/` → `/root/ros2_jazzy/install/lib/`
- **PLIKI WSPÓŁDZIELONE:** `/opt/ros/jazzy/share/` → `/root/ros2_jazzy/install/share/`

### Workspace użytkownika:
- **WORKSPACE UŻYTKOWNIKA:** `~/ros2_ws/` → `/root/ros2_jazzy/`
- **KOD ŹRÓDŁOWY PAKIETÓW:** `~/ros2_ws/src/` → `/root/ros2_jazzy/src/`
- **ZAINSTALOWANE PAKIETY:** `~/ros2_ws/install/` → `/root/ros2_jazzy/install/`
- **PLIKI BUDOWANIA:** `~/ros2_ws/build/` → `/root/ros2_jazzy/build/`
- **LOGI BUDOWANIA:** `~/ros2_ws/log/` → `/root/ros2_jazzy/log/`

### Kod projektu:
- **KOD ŹRÓDŁOWY PROJEKTU:** `~/src/` → `/root/src_ros2/`
- **KONFIGURACJA UŻYTKOWNIKA:** `~/.ros/` → `/root/config/`
- **LOGI APLIKACJI:** `~/logs/` → `/root/logs/`

### Skrypty aktywacji:
- **GŁÓWNY SETUP ROS:** `/opt/ros/jazzy/setup.bash` → `/root/ros2_jazzy/install/setup.bash`
- **SETUP WORKSPACE:** `~/ros2_ws/install/setup.bash` → `/root/ros2_jazzy/install/setup.bash`
- **ALTERNATYWNY SETUP:** (brak) → `/opt/ros2_setup.sh`

### Zmienne środowiskowe:
- **ROS_DOMAIN_ID:** `0` (domyślnie) → `42`
- **ROS_LOCALHOST_ONLY:** `1` (domyślnie) → `0`
- **COLCON_WS:** (brak) → `/root/ros2_jazzy`

## ⚠️ Kluczowe różnice

### 1. Typ instalacji:
- **METODA INSTALACJI:** Pakiety APT → Budowanie ze źródeł
- **CZAS INSTALACJI:** ~10 minut → ~2-3 godziny
- **KONTROLA:** Ograniczona → Pełna

### 2. Lokalizacja plików:
- **KATALOG GŁÓWNY:** `/opt/ros/jazzy/` → `/root/ros2_jazzy/`
- **WORKSPACE:** `~/ros2_ws/` → `/root/ros2_jazzy/`
- **KONFIGURACJA:** `~/.ros/` → `/root/config/`

### 3. Skrypty aktywacji:
- **GŁÓWNY SETUP:** `/opt/ros/jazzy/setup.bash` → `/root/ros2_jazzy/install/setup.bash`
- **DODATKOWY SETUP:** (brak) → `/opt/ros2_setup.sh`
- **AUTO-AKTYWACJA:** (brak) → `/root/.bashrc`

### 4. Zmienne środowiskowe:
- **ROS_DOMAIN_ID:** `0` → `42`
- **ROS_LOCALHOST_ONLY:** `1` → `0`
- **DODATKOWE ZMIENNE:** (brak) → `ARGUS_*`

## 🔧 Aktywacja środowiska

### Porównanie aktywacji:
- **STANDARDOWA AKTYWACJA:**
  ```bash
  source /opt/ros/jazzy/setup.bash
  source ~/ros2_ws/install/setup.bash
  ```

- **AKTYWACJA W ARGUS:**
  ```bash
  # Opcja 1: Przygotowany skrypt
  source /opt/ros2_setup.sh

  # Opcja 2: Bezpośrednio
  source /root/ros2_jazzy/install/setup.bash
  export ROS_DOMAIN_ID=42
  export ROS_LOCALHOST_ONLY=0

  # Opcja 3: Automatycznie (skonfigurowane w /root/.bashrc)
  ```

### Dodatkowe aliasy w ARGUS:
- **BUDOWANIE:** `colcon build` → `argus-build`
- **AKTYWACJA:** `source install/setup.bash` → `argus-source`
- **NAWIGACJA WORKSPACE:** `cd ~/ros2_ws` → `argus-ws`
- **NAWIGACJA ŹRÓDŁA:** `cd ~/src` → `argus-src`
- **NAWIGACJA LOGI:** `cd ~/logs` → `argus-logs`

## 🚀 Uruchamianie

### Porównanie uruchamiania:
- **STANDARDOWE URUCHAMIANIE:**
  ```bash
  cd ~/ros2_ws
  colcon build
  source install/setup.bash
  ros2 launch my_package my_launch.py
  ```

- **URUCHAMIANIE W ARGUS:**
  ```bash
  # Automatyczne (systemd)
  systemctl start argus_core

  # Ręczne
  /opt/argusik/bin/start_ros2.sh

  # Z aliasami
  argus-ws && argus-build
  ros2 launch robot_core argus_robot.launch.py
  ```

## 📝 Uwagi

1. **Zalety niestandardowej instalacji:**
   - Pełna kontrola nad środowiskiem ROS2
   - Brak konfliktów z systemowymi pakietami
   - Możliwość dostosowania do specyficznych potrzeb robota

2. **Wady:**
   - Brak kompatybilności ze standardowymi tutorialami
   - Trudniejsze debugowanie problemów
   - Wymagana znajomość niestandardowych ścieżek

3. **Ważne ścieżki do zapamiętania:**
   - Setup script: `/root/ros2_jazzy/install/setup.bash`
   - Alternatywny setup: `/opt/ros2_setup.sh`
   - Workspace: `/root/ros2_jazzy/`
   - Kod źródłowy: `/root/src_ros2/`
