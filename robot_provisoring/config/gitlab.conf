# ARGUS Robot GitLab Configuration
# Konfiguracja repozytoriow GitLab dla automatycznego pobierania kodu

# URL repozytorium robot_core
ROBOT_CORE_REPO_URL="https://gitlab.devforyou.pl/avotech/robot_core.git"

# Branch do pobrania (domyslnie main)
ROBOT_CORE_BRANCH="main"

# <PERSON> (dla self-hosted GitLab)
GITLAB_USERNAME="MAIL"
GITLAB_TOKEN="HASLO"

# Katalog docelowy
ROBOT_CORE_TARGET_DIR="/root/src_ros2"

# Opcje git clone (bez --depth dla self-hosted)
GIT_CLONE_OPTIONS="--single-branch"

# Konfiguracja dla self-hosted GitLab
GITLAB_SELF_HOSTED=true

# SSL/TLS - ustaw na false jesli self-hosted uzywa self-signed certificates
GIT_SSL_VERIFY=true

# Timeout dla operacji git (w sekundach)
GIT_TIMEOUT=300

# Dodatkowe opcje git dla HTTP
GIT_HTTP_LOW_SPEED_LIMIT=1000
GIT_HTTP_LOW_SPEED_TIME=60
