# ARGUS Robot Network Configuration
# Ten plik zawiera konfiguracje sieciowa dla robota ARGUS

# Access Point Configuration
AP_SSID="ARGUS"
AP_PASSWORD="argusik123"
AP_IP="***********"
AP_CHANNEL=7

# DHCP Configuration
DHCP_RANGE_START="***********"
DHCP_RANGE_END="************"
DHCP_LEASE_TIME="24h"

# Network Interface Configuration
WLAN_INTERFACE="wlan0"
ETH_INTERFACE="eth0"

# Streaming Configuration
RTSP_PORT=8554
HTTP_PORT=8080
WEBSOCKET_PORT=8081

# Security Configuration
ENABLE_SSH=true
SSH_PORT=22
ENABLE_FIREWALL=false

# Robot Identification
ROBOT_NAME="ARGUS-Robot"
ROBOT_ID="argus-001"