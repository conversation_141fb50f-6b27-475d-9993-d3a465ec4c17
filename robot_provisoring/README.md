# ARGUS Robot Provisioning 🤖

Automatyczny system provisioningu dla robota ARGUS na Raspberry Pi z ROS2 Jazzy.

## 🎯 Cel

Pelna automatyzacja konfiguracji robota ARGUS:
- Instalacja ROS2 Jazzy w `/root/`
- Konfiguracja wszystkich zaleznosci
- Uruchomienie uslug systemowych
- Konfiguracja Access Point
- Gotowy do uzycia robot po jednym poleceniu

## 🛠 Wymagania

- **Raspberry Pi 5**
- **Raspbian Bookworm**
- **Karta microSD**
- **Polaczenie internetowe** (preferowane LAN podczas instalacji)

## 🚀 Szybka instalacja

1. **Pobierz projekt:**
   ```bash
   git clone <repository-url>
   cd robot_provisioning
   ```

2. **Skonfiguruj GitLab (opcjonalne):**
   ```bash
   # Edytuj URL repozytorium robot_core
   nano config/gitlab.conf
   ```

3. **Uruchom instalacje:**
   ```bash
   sudo ./install.sh
   ```

3. **Poczekaj na zakonczenie** (30-60 minut)

4. **System zostanie automatycznie zrestartowany**

## 📦 Co zostanie zainstalowane

### System i podstawowe pakiety
- Aktualizacja systemu Raspbian Bookworm
- Podstawowe narzedzia deweloperskie
- Biblioteki do obslugi kamer i sensorow
- Konfiguracja interfejsow I2C, SPI, Camera

### ROS2 Jazzy
- Pelna instalacja ROS2 Jazzy Desktop
- Dodatkowe pakiety ROS2 (cv_bridge, camera drivers, etc.)
- Workspace w `/root/ros2_jazzy`
- Automatyczne sourcing srodowiska

### Python i biblioteki
- OpenCV, NumPy, Pillow
- Biblioteki Adafruit do obslugi hardware
- Biblioteki do obslugi kamer (picamera2)
- Biblioteki do streamingu (ffmpeg, rtsp)

### Siec i Access Point
- Konfiguracja Access Point (SSID: ARGUS-Robot)
- DHCP server (***********/24)
- NAT forwarding
- SSH server

### Uslugi systemowe
- `argus_core.service` - glowna usluga ROS2
- Automatyczne uruchamianie przy starcie
- Logowanie do `/var/log/argusik/`

## 📂 Struktura katalogow

```
/root/
├── ros2_jazzy/         # ROS2 workspace
│   ├── src/           # Skompilowany kod ROS2
│   ├── build/         # Pliki build
│   └── install/       # Zainstalowane pakiety
├── src_ros2/          # Kod zrodlowy z GitLab
│   └── robot_core/    # Projekt robot_core
├── config/            # Pliki konfiguracyjne
└── logs/              # Logi aplikacji

/opt/argusik/
├── bin/               # Skrypty wykonywalne
└── config/            # Konfiguracja systemowa
```

### 🔍 Opis struktury provisioningu

1. **files/** - gotowe pliki konfiguracyjne:
   - `launcher/` - launch files ROS2
   - `service/` - pliki streamingu RTSP
2. **scripts/** - skrypty wykonawcze:
   - `01-base.sh` - podstawowa konfiguracja
   - `02-env.sh` - srodowisko
   - `03-deps.sh` - zaleznosci Python
   - `04-ros2.sh` - instalacja ROS2
   - `05-wan.sh` - konfiguracja sieci
   - `06-services.sh` - uslugi systemowe
3. **network/** - konfiguracje sieciowe
4. **services/** - pliki systemd
5. **config/** - konfiguracja GitLab i inne ustawienia

## ⚙️ Konfiguracja GitLab

Przed instalacja mozesz skonfigurowac automatyczne pobieranie kodu robot_core z GitLab:

```bash
# Edytuj plik konfiguracyjny
nano config/gitlab.conf
```

Ustaw wlasciwy URL repozytorium:
```bash
ROBOT_CORE_REPO_URL="https://gitlab.com/twoja-nazwa/robot_core.git"
ROBOT_CORE_BRANCH="main"
```

**Dla repozytoriow prywatnych** dodaj dane uwierzytelniajace:
```bash
GITLAB_USERNAME="twoja-nazwa"
GITLAB_TOKEN="twoj-token"
```

Jesli nie skonfigurujesz GitLab, system uzyje lokalnego kodu z katalogu `../robot_core` jako fallback.

## 🌐 Dostep do robota

Po instalacji robot bedzie dostepny jako Access Point:

- **SSID:** `ARGUS-Robot`
- **Haslo:** `argus2024`
- **IP robota:** `***********`
- **SSH:** `ssh argus@***********` (haslo: argus2024)

## 🔧 Zarzadzanie uslugami

```bash
# Status uslugi glownej
sudo systemctl status argus_core

# Restart uslugi
sudo systemctl restart argus_core

# Logi uslugi
sudo journalctl -u argus_core -f

# Diagnostyka
sudo /opt/argusik/bin/argus_diagnostics.sh
```

## 🎮 Aliasy systemowe

Po instalacji dostepne beda aliasy:

```bash
# Dla uzytkownika root
argus-build      # Budowanie workspace ROS2
argus-source     # Source srodowiska ROS2
argus-ws         # Przejscie do workspace ROS2
argus-src        # Przejscie do kodu zrodlowego
argus-logs       # Przejscie do logow
argus-diag       # Diagnostyka systemu

# Dla uzytkownika argus (zarzadzanie)
argus-status     # Status uslugi
argus-restart    # Restart uslugi
argus-logs       # Podglad logow
argus-diag       # Diagnostyka systemu
```

## 🔌 Uslugi i porty

- **ROS2:** Domain ID 42, localhost only
- **RTSP Stream:** `rtsp://***********:8554/camera_stream`
- **HTTP:** Port 8080
- **SSH:** Port 22

## 🐛 Rozwiazywanie problemow

### Brak polaczenia z Access Point
```bash
sudo systemctl status hostapd
sudo systemctl status dnsmasq
```

### Problemy z ROS2
```bash
source /opt/ros/jazzy/setup.bash
ros2 node list
ros2 topic list
```

### Problemy z kamera
```bash
v4l2-ctl --list-devices
ls -la /dev/video*
```

### Logi systemowe
```bash
sudo journalctl -u argus_core -n 50
tail -f /var/log/argusik/*.log
```

## 🔒 Bezpieczenstwo

- ROS2 skonfigurowany tylko dla localhost
- Access Point z WPA2
- SSH wlaczony (zmien domyslne haslo!)
- Firewall opcjonalny (domyslnie wylaczony)

## 📞 Wsparcie

W przypadku problemow sprawdz:
1. Logi instalacji: `/var/log/argusik/`
2. Status uslug: `systemctl status argus_core`
3. Diagnostyke: `argus-diag`

---

**⚠️ UWAGA:** Podczas instalacji nastapi rozlaczenie z siecia WiFi. Zalecane jest uzycie polaczenia LAN.
