#!/bin/bash
# cleanup.sh - Skrypt do czyszczenia systemu przed ponowna instalacja

set -e

echo "=== ARGUS Cleanup Script ==="
echo "Ten skrypt usuwa pliki i konfiguracje ARGUS z systemu"
echo "UWAGA: To jest operacja nieodwracalna!"
echo ""

read -p "Czy na pewno chcesz wyczyścic system? (y/n): " confirm
if [ "$confirm" != "y" ]; then
    echo "Anulowano."
    exit 0
fi

echo "Rozpoczynam czyszczenie..."

# Zatrzymanie i usuniecie uslug
echo "Zatrzymywanie uslug ARGUS..."
systemctl stop argus_core 2>/dev/null || true
systemctl disable argus_core 2>/dev/null || true
rm -f /etc/systemd/system/argus_core.service
systemctl daemon-reload

# Usuniecie katalogow ARGUS
echo "Usuwanie katalogow ARGUS..."
rm -rf /opt/argusik
rm -rf /etc/argusik
rm -rf /var/log/argusik
rm -f /opt/ros2_setup.sh

# Czyszczenie .bashrc (usuniecie konfiguracji ARGUS)
echo "Czyszczenie konfiguracji .bashrc..."
if [ -f /root/.bashrc ]; then
    # Tworzenie kopii zapasowej
    cp /root/.bashrc /root/.bashrc.backup.$(date +%Y%m%d_%H%M%S)
    
    # Usuniecie sekcji ARGUS z .bashrc
    sed -i '/# ARGUS Robot Environment Variables/,/^$/d' /root/.bashrc
    sed -i '/# ROS2 Jazzy Environment/,/^$/d' /root/.bashrc
fi

if [ -f /home/<USER>/.bashrc ]; then
    cp /home/<USER>/.bashrc /home/<USER>/.bashrc.backup.$(date +%Y%m%d_%H%M%S)
    sed -i '/# ARGUS Robot Management Aliases/,/^$/d' /home/<USER>/.bashrc
fi

echo ""
echo "✅ Czyszczenie zakonczone!"
echo ""
echo "Kopia zapasowa .bashrc zostala utworzona z rozszerzeniem .backup.*"
echo "Mozesz teraz uruchomic instalacje ponownie."
echo ""
echo "Aby uruchomic ponowna instalacje:"
