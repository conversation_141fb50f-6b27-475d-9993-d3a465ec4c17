#!/bin/bash
# restore-wifi.sh - Przywracanie normalnej konfiguracji WiFi (klient)

set -e

echo "=== Przywracanie normalnej konfiguracji WiFi ==="

# Sprawdź czy NetworkManager jest aktywny
if ! systemctl is-active --quiet NetworkManager; then
    echo "Włączanie NetworkManager..."
    systemctl enable NetworkManager
    systemctl start NetworkManager
    sleep 3
fi

# Rozłącz wszystkie aktywne połączenia na wlan0
echo "Rozłączanie aktywnych połączeń na wlan0..."
nmcli con show --active | grep wlan0 | awk '{print $1}' | while read con; do
    echo "Rozłączanie: $con"
    nmcli con down "$con" 2>/dev/null || true
done

# Rozłącz interfejs wlan0
nmcli dev disconnect wlan0 2>/dev/null || true

# Usuń Access Point ARGUS-AP
echo "Usuwanie Access Point ARGUS-AP..."
if nmcli con show | grep -q "ARGUS-AP"; then
    nmcli con delete "ARGUS-AP"
    echo "✓ Access Point ARGUS-AP został usunięty"
else
    echo "ℹ Access Point ARGUS-AP nie istnieje"
fi

# Usuń wszystkie połączenia typu Access Point
echo "Usuwanie wszystkich połączeń Access Point..."
nmcli con show | awk '{print $1}' | while read con; do
    # Sprawdź czy to nie jest nagłówek tabeli
    if [ "$con" != "NAME" ] && [ -n "$con" ]; then
        # Sprawdź czy to połączenie AP
        if nmcli con show "$con" 2>/dev/null | grep -q "802-11-wireless.mode.*ap"; then
            echo "Usuwanie AP: $con"
            nmcli con delete "$con" 2>/dev/null || true
        fi
    fi
done

# Wyłącz i włącz radio WiFi
echo "Resetowanie interfejsu WiFi..."
nmcli radio wifi off
sleep 3
nmcli radio wifi on
sleep 5

# Usuń reguły iptables NAT (jeśli istnieją)
echo "Usuwanie reguł NAT..."
# Sprawdź czy reguły istnieją przed ich usunięciem
if iptables -t nat -C POSTROUTING -o eth0 -j MASQUERADE 2>/dev/null; then
    iptables -t nat -D POSTROUTING -o eth0 -j MASQUERADE
    echo "✓ Usunięto regułę NAT MASQUERADE"
fi

if iptables -C FORWARD -i eth0 -o wlan0 -m state --state RELATED,ESTABLISHED -j ACCEPT 2>/dev/null; then
    iptables -D FORWARD -i eth0 -o wlan0 -m state --state RELATED,ESTABLISHED -j ACCEPT
    echo "✓ Usunięto regułę FORWARD eth0->wlan0"
fi

if iptables -C FORWARD -i wlan0 -o eth0 -j ACCEPT 2>/dev/null; then
    iptables -D FORWARD -i wlan0 -o eth0 -j ACCEPT
    echo "✓ Usunięto regułę FORWARD wlan0->eth0"
fi

# Zapisz zmiany iptables
if command -v netfilter-persistent >/dev/null 2>&1; then
    netfilter-persistent save
    echo "✓ Zapisano zmiany iptables"
fi

# Usuń konfigurację IP forwarding (opcjonalnie)
echo "Wyłączanie IP forwarding..."
if [ -f /etc/sysctl.d/routed-ap.conf ]; then
    rm /etc/sysctl.d/routed-ap.conf
    echo "✓ Usunięto konfigurację IP forwarding"
fi

# Zastosuj zmiany sysctl
sysctl net.ipv4.ip_forward=0 2>/dev/null || true

echo "=== Sprawdzanie końcowego stanu ==="

# Sprawdź czy nie ma aktywnych połączeń AP
if nmcli con show --active | grep -q "ap\|hotspot"; then
    echo "⚠ Ostrzeżenie: Nadal aktywne połączenia Access Point"
    nmcli con show --active | grep -E "ap|hotspot"
else
    echo "✓ Brak aktywnych połączeń Access Point"
fi

# Sprawdź status interfejsu wlan0
WLAN0_STATUS=$(nmcli dev status | grep wlan0 | awk '{print $3}')
echo "Status wlan0: $WLAN0_STATUS"

echo ""
echo "=== Konfiguracja przywrócona ==="
echo ""
echo "Robot jest teraz gotowy do połączenia z normalną siecią WiFi."
echo ""
echo "Dostępne sieci WiFi:"
nmcli dev wifi list | head -10
echo ""
echo "Aby połączyć się z siecią WiFi użyj:"
echo "  nmcli dev wifi connect 'NAZWA_SIECI' password 'HASŁO'"
echo ""
echo "Lub użyj interaktywnego narzędzia:"
echo "  nmtui"
echo ""
echo "Status wszystkich interfejsów:"
nmcli dev status

# Opcjonalnie: automatyczne skanowanie sieci
echo ""
echo "Skanowanie dostępnych sieci..."
nmcli dev wifi rescan 2>/dev/null || true
