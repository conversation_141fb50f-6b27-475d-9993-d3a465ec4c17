#!/bin/bash
# 06-services.sh - Konfiguracja uslug systemowych i aplikacji ARGUS

set -e

echo "=== Konfiguracja uslug systemowych ==="

# Tworzenie katalogow dla logow i skryptow
echo "Tworzenie katalogow systemowych..."
mkdir -p /var/log/argusik
mkdir -p /opt/argusik/bin
mkdir -p /opt/argusik/config

# Okreslenie sciezki do katalogu provisioning
PROVISION_DIR="$(dirname "$(dirname "$(realpath "$0")")")"

# Kopiowanie i instalacja uslugi systemowej
echo "Instalacja uslugi systemowej..."
if [ -f "$PROVISION_DIR/services/argus_core.service" ]; then
    cp "$PROVISION_DIR/services/argus_core.service" /etc/systemd/system/
    systemctl daemon-reload
    systemctl enable argus_core.service
fi

# Tworzenie struktury katalogow ROS2 (jesli nie istnieja)
echo "Sprawdzanie struktury katalogow ROS2..."
mkdir -p /root/src_ros2
mkdir -p /root/ros2_jazzy/src

# Pobieranie kodu zrodlowego robot_core z GitLab
echo "Pobieranie kodu zrodlowego robot_core z GitLab..."
cd /root/src_ros2

# Sprawdzenie czy git jest dostepny
if ! command -v git &> /dev/null; then
    echo "BlaD: Git nie jest zainstalowany!"
    exit 1
fi

# Wczytanie konfiguracji GitLab
GITLAB_CONFIG="$PROVISION_DIR/config/gitlab.conf"
if [ -f "$GITLAB_CONFIG" ]; then
    echo "Wczytywanie konfiguracji GitLab z $GITLAB_CONFIG"
    source "$GITLAB_CONFIG"
else
    echo "UWAGA: Nie znaleziono pliku konfiguracyjnego GitLab, uzywam domyslnych wartosci"
    ROBOT_CORE_REPO_URL="https://gitlab.devforyou.pl/avotech/robot_core.git"
    ROBOT_CORE_BRANCH="master"
    GIT_CLONE_OPTIONS="--single-branch"
fi

echo "Klonowanie repozytorium: $ROBOT_CORE_REPO_URL (branch: $ROBOT_CORE_BRANCH)"

# Konfiguracja git dla self-hosted GitLab
if [ "${GITLAB_SELF_HOSTED:-false}" = "true" ]; then
    echo "Konfiguracja git dla self-hosted GitLab..."

    # SSL verification
    if [ "${GIT_SSL_VERIFY:-true}" = "false" ]; then
        echo "Wylaczanie weryfikacji SSL dla self-hosted GitLab"
        git config --global http.sslVerify false
    fi

    # HTTP timeouts
    git config --global http.lowSpeedLimit "${GIT_HTTP_LOW_SPEED_LIMIT:-1000}"
    git config --global http.lowSpeedTime "${GIT_HTTP_LOW_SPEED_TIME:-60}"
    git config --global http.postBuffer 524288000
fi

# Klonowanie z timeout
if timeout "${GIT_TIMEOUT:-300}" git clone $GIT_CLONE_OPTIONS --branch "$ROBOT_CORE_BRANCH" "$ROBOT_CORE_REPO_URL" robot_core; then
    echo "Pomyslnie pobrano robot_core z GitLab"

    # Linkowanie pakietow do workspace ROS2
    echo "Linkowanie pakietow robot_core do workspace ROS2..."
    if [ -d "/root/src_ros2/robot_core" ]; then
        # Usun stare linki jesli istnieja
        find /root/ros2_jazzy/src/ -type l -delete 2>/dev/null || true

        # Linkuj kazdy pakiet ROS2 osobno
        for package_dir in /root/src_ros2/robot_core/*/; do
            if [ -f "$package_dir/package.xml" ]; then
                package_name=$(basename "$package_dir")
                echo "Linkowanie pakietu: $package_name"
                ln -sf "$package_dir" "/root/ros2_jazzy/src/$package_name"
            fi
        done

        # Jesli robot_core ma glowny package.xml, linkuj tez glowny katalog
        if [ -f "/root/src_ros2/robot_core/package.xml" ]; then
            echo "Linkowanie glownego pakietu robot_core"
            ln -sf "/root/src_ros2/robot_core" "/root/ros2_jazzy/src/robot_core"
        fi

        echo "Pakiety robot_core zlinkowane do workspace"
    else
        echo "BlaD: Nie znaleziono pobranego katalogu robot_core"
        exit 1
    fi
else
    echo "BlaD: Nie udalo sie pobrac repozytorium robot_core"
    echo "Sprawdz:"
    echo "1. URL repozytorium: $ROBOT_CORE_REPO_URL"
    echo "2. Polaczenie z self-hosted GitLab"
    echo "3. Dane uwierzytelniajace (jesli repo prywatne)"
    echo "4. Certyfikaty SSL (ustaw GIT_SSL_VERIFY=false jesli potrzeba)"

    # Test polaczenia z GitLab
    GITLAB_HOST=$(echo "$ROBOT_CORE_REPO_URL" | sed -n 's|https\?://\([^/]*\).*|\1|p')
    if [ -n "$GITLAB_HOST" ]; then
        echo "Testowanie polaczenia z $GITLAB_HOST..."
        if ping -c 3 "$GITLAB_HOST" &>/dev/null; then
            echo "✓ Polaczenie sieciowe z $GITLAB_HOST dziala"
        else
            echo "✗ Brak polaczenia sieciowego z $GITLAB_HOST"
        fi

        if curl -s --connect-timeout 10 "https://$GITLAB_HOST" &>/dev/null; then
            echo "✓ HTTPS polaczenie z $GITLAB_HOST dziala"
        else
            echo "✗ Problem z HTTPS polaczeniem do $GITLAB_HOST"
        fi
    fi

    exit 1
fi

echo "Pakiety robot_core zostaly zlinkowane do workspace ROS2"
echo "UWAGA: Jesli pakiety wymagaja budowania, uruchom recznie:"
echo "  cd /root/ros2_jazzy && source install/setup.bash && colcon build"

# Konfiguracja logowania
echo "Konfiguracja logowania..."
cat > /etc/logrotate.d/argusik << 'EOF'
/var/log/argusik/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create 644 root root
}
EOF

# Tworzenie skryptu diagnostycznego
echo "Tworzenie skryptu diagnostycznego..."
cat > /opt/argusik/bin/argus_diagnostics.sh << 'EOF'
#!/bin/bash
# ARGUS Robot Diagnostics Script

echo "=== ARGUS Robot Diagnostics ==="
echo "Date: $(date)"
echo "Hostname: $(hostname)"
echo "IP Address: $(hostname -I | tr -d ' ')"
echo ""

echo "=== System Info ==="
echo "OS: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)"
echo "Kernel: $(uname -r)"
echo "Architecture: $(uname -m)"
echo "Uptime: $(uptime -p)"
echo "Load: $(uptime | awk -F'load average:' '{print $2}')"
echo "Memory: $(free -h | grep Mem | awk '{print $3"/"$2}')"
echo ""

echo "=== Raspberry Pi Hardware ==="
if command -v vcgencmd >/dev/null 2>&1; then
    echo "Temperature: $(vcgencmd measure_temp)"
    echo "Throttling: $(vcgencmd get_throttled)"
    echo "GPU Memory: $(vcgencmd get_mem gpu)"
    echo "ARM Memory: $(vcgencmd get_mem arm)"
else
    echo "vcgencmd not available (not on Raspberry Pi?)"
fi
echo ""

echo "=== ROS2 Status ==="
if [ -f "/root/ros2_jazzy/install/setup.bash" ]; then
    source /root/ros2_jazzy/install/setup.bash 2>/dev/null
    echo "ROS_DISTRO: ${ROS_DISTRO:-not set}"
    echo "ROS_DOMAIN_ID: ${ROS_DOMAIN_ID:-not set}"
    echo "ROS_LOCALHOST_ONLY: ${ROS_LOCALHOST_ONLY:-not set}"
    echo "ROS2 Workspace: /root/ros2_jazzy"

    # Sprawdź czy ROS2 działa
    if command -v ros2 >/dev/null 2>&1; then
        echo "ROS2 Command: Available"
        echo "ROS2 Nodes:"
        timeout 5 ros2 node list 2>/dev/null || echo "  No nodes running or timeout"
    else
        echo "ROS2 Command: Not available"
    fi
else
    echo "ROS2 not installed or setup.bash not found"
fi
echo ""

echo "=== Services Status ==="
services=("argus_core" "NetworkManager")
for service in "${services[@]}"; do
    if systemctl is-active --quiet "$service"; then
        echo "$service: ✓ active"
    else
        echo "$service: ✗ inactive"
    fi
done
echo ""

echo "=== Network Status ==="
echo "NetworkManager connections:"
nmcli con show --active 2>/dev/null || echo "NetworkManager not available"
echo ""
echo "Interface status:"
for iface in wlan0 eth0; do
    if ip addr show "$iface" >/dev/null 2>&1; then
        ip_addr=$(ip addr show "$iface" | grep "inet " | awk '{print $2}' | head -1)
        if [ -n "$ip_addr" ]; then
            echo "$iface: $ip_addr"
        else
            echo "$iface: no IP"
        fi
    else
        echo "$iface: not found"
    fi
done
echo ""

echo "=== Storage Status ==="
df -h / /boot 2>/dev/null || df -h /
echo ""

echo "=== Camera Devices ==="
ls -la /dev/video* 2>/dev/null || echo "No video devices found"
echo ""

echo "=== Recent ARGUS Logs ==="
if [ -d "/var/log/argusik" ]; then
    for logfile in /var/log/argusik/*.log; do
        if [ -f "$logfile" ]; then
            echo "--- $(basename "$logfile") (last 5 lines) ---"
            tail -n 5 "$logfile" 2>/dev/null
        fi
    done
else
    echo "No ARGUS log directory found"
fi
echo ""

echo "=== System Logs (last 10 lines) ==="
journalctl -u argus_core --no-pager -n 10 2>/dev/null || echo "No argus_core service logs"
EOF

chmod +x /opt/argusik/bin/argus_diagnostics.sh

# Dodanie aliasu dla diagnostyki (tylko dla root, dla argusik już jest w 02-env.sh)
echo "Dodawanie aliasu diagnostycznego dla root..."
if ! grep -q "alias argus-diag=" /root/.bashrc; then
    echo "alias argus-diag='/opt/argusik/bin/argus_diagnostics.sh'" >> /root/.bashrc
    echo "✓ Alias argus-diag dodany dla root"
else
    echo "ℹ Alias argus-diag już istnieje dla root"
fi

echo "=== Konfiguracja uslug zakonczona ==="