#!/bin/bash
# 02-env.sh - Konfiguracja srodowiska i zmiennych

set -e

echo "=== Konfiguracja srodowiska ==="

# Tworzenie katalogow roboczych
echo "Tworzenie katalogow roboczych..."
mkdir -p /root/ros2_jazzy/src          # ROS2 workspace
mkdir -p /root/src_ros2                # Kod zrodlowy z GitLab
mkdir -p /root/logs
mkdir -p /root/config
mkdir -p /opt/argusik

# Konfiguracja zmiennych srodowiskowych w .bashrc dla root
echo "Konfiguracja zmiennych srodowiskowych..."
# Sprawdz czy konfiguracja juz istnieje
if ! grep -q "ARGUS Robot Environment Variables" /root/.bashrc; then
    cat >> /root/.bashrc << 'EOF'

# ARGUS Robot Environment Variables
export ARGUS_ROOT="/root"
export ARGUS_SRC="/root/src_ros2"           # Kod zrodlowy z GitLab
export ARGUS_WS="/root/ros2_jazzy"          # ROS2 workspace
export ARGUS_CONFIG="/root/config"
export ARGUS_LOGS="/root/logs"

# Python packages installed via apt - no venv needed

# ROS2 Environment (bedzie dodane po instalacji ROS2)
# source /opt/ros/jazzy/setup.bash
# source $ARGUS_WS/install/setup.bash

# Python path dla ARGUS
export PYTHONPATH="$ARGUS_SRC:$ARGUS_WS/src:$PYTHONPATH"

# Aliasy dla ARGUS
alias argus-build='cd $ARGUS_WS && colcon build'
alias argus-source='source $ARGUS_WS/install/setup.bash'
alias argus-logs='cd $ARGUS_LOGS'
alias argus-ws='cd $ARGUS_WS'
alias argus-src='cd $ARGUS_SRC'

EOF
else
    echo "Konfiguracja zmiennych środowiskowych juz istnieje - pomijam"
fi

# Konfiguracja podstawowych aliasow dla uzytkownika argus (tylko do zarzadzania)
echo "Konfiguracja aliasow zarzadzania dla uzytkownika argus..."
# Sprawdz czy konfiguracja juz istnieje
if [ -f "/home/<USER>/.bashrc" ] && ! grep -q "ARGUS Management Aliases" /home/<USER>/.bashrc; then
    cat >> /home/<USER>/.bashrc << 'EOF'

# ARGUS Robot Management Aliases
alias argus-status='sudo systemctl status argus_core'
alias argus-restart='sudo systemctl restart argus_core'
alias argus-stop='sudo systemctl stop argus_core'
alias argus-start='sudo systemctl start argus_core'
alias argus-logs='sudo journalctl -u argus_core -f'
alias argus-diag='sudo /opt/argusik/bin/argus_diagnostics.sh'

EOF
else
    echo "Aliasy zarzadzania juz istnieja - pomijam"
fi

# Ustawienie wlasciciela dla katalogu uzytkownika argus
chown argusik:argusik /home/<USER>/.bashrc 2>/dev/null || true

# Konfiguracja locale
echo "Konfiguracja locale..."

# Najpierw sprawdz czy locale jest dostepne w /etc/locale.gen i odkomentuj
if grep -q "^# en_US.UTF-8" /etc/locale.gen; then
    echo "Odkomentowywanie en_US.UTF-8 w /etc/locale.gen"
    sed -i 's/^# en_US\.UTF-8 UTF-8/en_US.UTF-8 UTF-8/' /etc/locale.gen
fi

locale-gen en_US.UTF-8
update-locale LANG=en_US.UTF-8

# Konfiguracja timezone
echo "Konfiguracja timezone..."
timedatectl set-timezone Europe/Warsaw

# Konfiguracja hostname
echo "Konfiguracja hostname..."
hostnamectl set-hostname argus-robot

# Aktualizacja /etc/hosts
echo "*********    argus-robot" >> /etc/hosts

echo "=== Konfiguracja srodowiska zakonczona ==="