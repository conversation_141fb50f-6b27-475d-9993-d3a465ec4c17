#!/bin/bash
# 03-deps.sh - Instalacja zaleznosci Python i bibliotek

set -e

echo "=== Instalacja zaleznosci Python i bibliotek ==="

# Instalacja wszystkich pakietow Python przez apt (bez venv)
echo "Instalacja bibliotek Python przez apt..."
apt install -y \
    python3-pip \
    python3-numpy \
    python3-opencv \
    python3-pil \
    python3-serial \
    python3-smbus \
    python3-rpi.gpio \
    python3-luma.oled \
    python3-luma.core \
    python3-luma.emulator \
    python3-flask \
    python3-flask-socketio \
    python3-websockets \
    python3-psutil


# Instalacja dodatkowych narzedzi
echo "Instalacja dodatkowych narzedzi..."
apt install -y \
    ffmpeg \
    v4l-utils \
    motion \
    supervisor

# Konfiguracja uprawnien dla urzadzen
echo "Konfiguracja uprawnien urzadzen..."
usermod -a -G video,audio,dialout root
usermod -a -G video,audio,dialout argusik 2>/dev/null || true

# Tworzenie linkow symbolicznych dla urzadzen
echo "Konfiguracja urzadzen..."
# Dodanie regul udev dla stabilnych nazw urzadzen
cat > /etc/udev/rules.d/99-argus-devices.rules << 'EOF'
# ARGUS Robot device rules
SUBSYSTEM=="video4linux", ATTRS{idVendor}=="*", ATTRS{idProduct}=="*", SYMLINK+="argus_camera%n"
SUBSYSTEM=="tty", ATTRS{idVendor}=="1a86", ATTRS{idProduct}=="7523", SYMLINK+="argus_serial"
EOF

# Przeladowanie regul udev
udevadm control --reload-rules
udevadm trigger

echo "=== Instalacja zaleznosci zakonczona ==="