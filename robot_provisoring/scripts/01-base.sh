#!/bin/bash
# 01-base.sh - Podstawowa konfiguracja systemu i instalacja pakietow

set -e

echo "=== Aktualizacja systemu i instalacja podstawowych pakietow ==="

# Aktualizacja listy pakietow
echo "Aktualizacja listy pakietow..."
apt update -qq

# Upgrade systemu
echo "Aktualizacja systemu..."
apt upgrade -y

# Instalacja podstawowych narzedzi
echo "Instalacja podstawowych narzedzi..."
apt install -y \
    git \
    curl \
    wget \
    vim \
    nano \
    htop \
    tree \
    unzip \
    build-essential \
    cmake \
    pkg-config \
    libjpeg-dev \
    libtiff5-dev \
    libpng-dev \
    libavcodec-dev \
    libavformat-dev \
    libswscale-dev \
    libv4l-dev \
    libxvidcore-dev \
    libx264-dev \
    libgtk-3-dev \
    libatlas-base-dev \
    gfortran \
    python3-dev \
    python3-pip \
    python3-venv \
    python3-setuptools \
    python3-wheel \
    i2c-tools \
    raspi-config

# Wlaczenie I2C, SPI, Camera
echo "Konfiguracja interfejsow Raspberry Pi..."
raspi-config nonint do_i2c 0      # Wlacz I2C
raspi-config nonint do_spi 0      # Wlacz SPI
raspi-config nonint do_camera 0   # Wlacz Camera
raspi-config nonint do_ssh 0      # Wlacz SSH

# Dodanie uzytkownika do grup
echo "Konfiguracja uprawnien uzytkownika..."
usermod -a -G i2c,spi,gpio,video argusik 2>/dev/null || true

# Konfiguracja GPU memory split
echo "Konfiguracja GPU memory..."
echo "gpu_mem=128" >> /boot/config.txt

echo "=== Podstawowa konfiguracja zakonczona ==="