from launch import LaunchDescription
from launch_ros.actions import Node


def generate_launch_description():
    return LaunchDescription([
        Node(
            package='oled_subscriber',
            namespace='oled',
            executable='oled',
            name='OLED'
        ),
        Node(
            package='sensors',
            namespace='ads',
            executable='ads',
            name='TEMP_MONITOR'
        ),
        Node(
            package='py_motors',
            namespace='pwm',
            executable='pwm',
            name='MOTOR_DRIVER'
        ),
        Node(
            package='sensors',
            namespace='power',
            executable='power',
            name='POWER_MONITOR'
        ),
        Node(
            package='sensors',
            namespace='gyro',
            executable='gyro',
            name='GYRO_MONITOR'
        ),
    
    ])