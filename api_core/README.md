# 🚀 Nazwa Projektu

Aplikacja backendowa zbudowana przy użyciu [AdonisJS](https://adonisjs.com/) – Node.js frameworka typu MVC z bogatym zestawem funkcji, takich jak uwierzytelnianie, walidacja, ORM, migracje i wiele więcej.

## 🧰 Technologie

- [AdonisJS v5+](https://docs.adonisjs.com/)
- Node.js (v18+ zalecane)
- PostgreSQL / MySQL / SQLite (do wyboru)
- Lucid ORM
- TypeScript

## 🗂 Struktura katalogów

```
.
├── app/              # Kontrolery, modele, middleware, validatory
├── config/           # Konfiguracja aplikacji
├── contracts/        # Interfejsy i definicje typów
├── database/
│   ├── migrations/   # Migracje bazy danych
│   └── seeders/      # Pliki seedujące
├── public/           # Publicznie dostępne zasoby (dla aplikacji web)
├── start/            # Pliki startowe
├── tests/            # Testy jednostkowe/HTTP
├── .env              # Zmienne środowiskowe
└── ace.js         # Główny punkt wejścia aplikacji
```

## ⚙️ Instalacja

```bash
git clone https://gitlab.devforyou.pl/avotech/api_core
cd api_core
cp .env.example .env
yarn
```

## 🛠 Konfiguracja

W pliku `.env` ustaw dane dostępowe do bazy danych na swoje:

```
DB_HOST=127.0.0.1
DB_PORT=3306
DB_USER=root
DB_PASSWORD=root
DB_DATABASE=app
```

## 🗄 Migracje bazy danych

```bash
node ace migration:run
```

Aby utworzyć nową migrację:

```bash
node ace make:migration nazwa_tabeli
```

## 🚀 Uruchomienie aplikacji

```bash
yarn dev
```

Lub w trybie produkcyjnym:

```bash
yarn build
yarn start
```

## 🧪 Testy

```bash
node ace test
```

## 📦 Przydatne komendy

| Komenda                        | Opis                                      |
|-------------------------------|-------------------------------------------|
| node ace list:routes          | Wyświetla listę wszystkich routów        |
| node ace make:controller      | Tworzy nowy kontroler                    |
| node ace make:model           | Tworzy nowy model                        |
| node ace migration:rollback   | Cofnięcie migracji                       |
| node ace generate:key         | Generuje nowy klucz aplikacji (.env)    |


## 👨‍💻 Autorzy

- [Krzysztof Włodarski](https://github.com/ziut3k-dev)

