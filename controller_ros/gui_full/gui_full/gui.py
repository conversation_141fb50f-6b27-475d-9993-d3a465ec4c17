#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from sensor_msgs.msg import Image, Imu
from geometry_msgs.msg import Twist
from cv_bridge import CvBridge
import cv2
import math
import numpy as np
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QSlider, QGroupBox, QGridLayout, QCheckBox
)
from PyQt5.QtCore import Qt, QTimer, QPoint, QPointF, QPropertyAnimation, pyqtProperty, QEasingCurve
from PyQt5.QtGui import (QImage, QPixmap, QPainter, QPen, QColor, 
                         QFont, QPolygon, QLinearGradient, QConicalGradient, QTransform, QPolygonF)

class CompassWidget(QLabel):
    def __init__(self, parent=None):
        super().__init__(parent)
        self._heading = 0
        self._target_heading = 0
        self._animation = QPropertyAnimation(self, b"heading")
        self._animation.setDuration(500)
        self._animation.setEasingCurve(QEasingCurve.OutQuad)
        self.setMinimumSize(200, 200)
        
    def set_target_heading(self, heading):
        self._target_heading = heading
        self._animation.stop()
        self._animation.setStartValue(self._heading)
        self._animation.setEndValue(heading)
        self._animation.start()
        
    def get_heading(self):
        return self._heading
        
    def set_heading(self, heading):
        self._heading = heading
        self.update()
        
    heading = pyqtProperty(float, get_heading, set_heading)
    
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        size = min(self.width(), self.height())
        center = QPoint(self.width() // 2, self.height() // 2)
        radius = size // 2 - 10
        
        # Draw outer metallic ring
        gradient = QLinearGradient(center.x() - radius, center.y() - radius,
                                 center.x() + radius, center.y() + radius)
        gradient.setColorAt(0, QColor(200, 200, 200))
        gradient.setColorAt(1, QColor(100, 100, 100))
        
        painter.setPen(QPen(QColor(80, 80, 80), 3))
        painter.setBrush(gradient)
        painter.drawEllipse(center, radius, radius)
        
        # Draw inner compass face with radial gradient
        inner_radius = radius - 15
        face_gradient = QConicalGradient(center, -self._heading)
        face_gradient.setColorAt(0, QColor(240, 240, 240))
        face_gradient.setColorAt(0.5, QColor(220, 220, 220))
        face_gradient.setColorAt(1, QColor(240, 240, 240))
        
        painter.setPen(QPen(Qt.black, 1))
        painter.setBrush(face_gradient)
        painter.drawEllipse(center, inner_radius, inner_radius)
        
        # Draw cardinal directions with rotation
        painter.save()
        painter.translate(center.x(), center.y())
        painter.rotate(-self._heading)
        
        font = QFont('Arial', 10)
        font.setBold(True)
        painter.setFont(font)
        
        cardinals = [
            (0, "N", Qt.red),
            (90, "E", Qt.black),
            (180, "S", Qt.black),
            (270, "W", Qt.black)
        ]
        
        for angle, text, color in cardinals:
            angle_rad = math.radians(angle)
            text_radius = inner_radius - 20
            x = text_radius * math.sin(angle_rad)
            y = -text_radius * math.cos(angle_rad)
            
            painter.setPen(QPen(color))
            text_width = painter.fontMetrics().width(text)
            painter.drawText(int(x - text_width/2), int(y + 5), text)
        
        # Draw degree marks
        painter.setPen(QPen(Qt.black, 1))
        for angle in range(0, 360, 10):
            angle_rad = math.radians(angle)
            if angle % 30 == 0:
                inner = inner_radius - 10
                outer = inner_radius - 2
            else:
                inner = inner_radius - 5
                outer = inner_radius - 2
            
            x1 = inner * math.sin(angle_rad)
            y1 = -inner * math.cos(angle_rad)
            x2 = outer * math.sin(angle_rad)
            y2 = -outer * math.cos(angle_rad)
            painter.drawLine(x1, y1, x2, y2)
        
        painter.restore()
        
        # Draw compass needle (always points north)
        needle_length = inner_radius - 10
        painter.setPen(QPen(Qt.red, 2))
        north_needle = QPolygon([
            QPoint(center.x(), center.y() - needle_length),
            QPoint(center.x() - 10, center.y() - needle_length + 20),
            QPoint(center.x() + 10, center.y() - needle_length + 20)
        ])
        painter.setBrush(Qt.red)
        painter.drawPolygon(north_needle)
        
        # Draw center pin
        painter.setPen(QPen(Qt.black, 2))
        painter.setBrush(QColor(150, 150, 150))
        painter.drawEllipse(center, 8, 8)
        
        # Draw current heading
        font = QFont('Arial', 12)
        font.setBold(True)
        painter.setFont(font)
        painter.setPen(QPen(Qt.red))
        painter.drawText(center.x() - 25, 30, f"{int(self._heading % 360)}°")

class Robot3DWidget(QLabel):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.roll = 0
        self.pitch = 0
        self.yaw = 0
        self.setMinimumSize(200, 200)
        self.setStyleSheet("background-color: black;")
        
    def set_orientation(self, roll, pitch, yaw):
        self.roll = math.degrees(roll)
        self.pitch = math.degrees(pitch)
        self.yaw = math.degrees(yaw)
        self.update()
        
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        width = self.width()
        height = self.height()
        center = QPoint(width // 2, height // 2)
        size = min(width, height) * 0.4
        
        # Draw 3D robot representation
        self.draw_robot_3d(painter, center, size)
        
        # Draw orientation text
        font = QFont('Arial', 10)
        painter.setFont(font)
        painter.setPen(QPen(Qt.white))
        painter.drawText(10, 20, f"Roll: {self.roll:.1f}°")
        painter.drawText(10, 40, f"Pitch: {self.pitch:.1f}°")
        painter.drawText(10, 60, f"Yaw: {self.yaw:.1f}°")
        
    def draw_robot_3d(self, painter, center, size):
        # Create 3D cube points
        points = [
            [-1, -1, -1], [1, -1, -1], [1, 1, -1], [-1, 1, -1],
            [-1, -1, 1], [1, -1, 1], [1, 1, 1], [-1, 1, 1]
        ]
        
        # Apply rotations
        roll = math.radians(self.roll)
        pitch = math.radians(self.pitch)
        yaw = math.radians(self.yaw)
        
        rotated_points = []
        for point in points:
            x, y, z = point
            
            # Apply yaw (Z-axis rotation)
            new_x = x * math.cos(yaw) - y * math.sin(yaw)
            new_y = x * math.sin(yaw) + y * math.cos(yaw)
            new_z = z
            
            # Apply pitch (Y-axis rotation)
            x = new_x * math.cos(pitch) + new_z * math.sin(pitch)
            z = -new_x * math.sin(pitch) + new_z * math.cos(pitch)
            y = new_y
            
            # Apply roll (X-axis rotation)
            new_y = y * math.cos(roll) - z * math.sin(roll)
            new_z = y * math.sin(roll) + z * math.cos(roll)
            new_x = x
            
            rotated_points.append([new_x, new_y, new_z])
        
        # Project 3D to 2D
        projected_points = []
        for point in rotated_points:
            x, y, z = point
            factor = 2 / (2 + z * 0.5)
            px = center.x() + x * size * factor
            py = center.y() + y * size * factor
            projected_points.append(QPointF(px, py))
        
        # Draw cube faces
        faces = [
            [0, 1, 2, 3], [4, 5, 6, 7], [0, 1, 5, 4],
            [2, 3, 7, 6], [1, 2, 6, 5], [0, 3, 7, 4]
        ]
        
        face_colors = [
            QColor(255, 0, 0, 150), QColor(0, 255, 0, 150),
            QColor(0, 0, 255, 150), QColor(255, 255, 0, 150),
            QColor(255, 0, 255, 150), QColor(0, 255, 255, 150)
        ]
        
        # Sort faces by Z-depth
        face_order = []
        for i, face in enumerate(faces):
            z = sum(rotated_points[idx][2] for idx in face) / len(face)
            face_order.append((z, i))
        
        for z, i in sorted(face_order, reverse=True):
            face = faces[i]
            polygon = QPolygonF([projected_points[idx] for idx in face])
            painter.setBrush(face_colors[i])
            painter.setPen(QPen(Qt.white, 1))
            painter.drawPolygon(polygon)
        
        # Draw axes
        axis_points = [
            [2, 0, 0], [0, 2, 0], [0, 0, 2]
        ]
        
        projected_axes = []
        for point in axis_points:
            x, y, z = point
            factor = 2 / (2 + z * 0.5)
            px = center.x() + x * size * factor
            py = center.y() + y * size * factor
            projected_axes.append(QPointF(px, py))
        
        colors = [Qt.red, Qt.green, Qt.blue]
        labels = ["X", "Y", "Z"]
        for i in range(3):
            painter.setPen(QPen(colors[i], 2))
            painter.drawLine(center, projected_axes[i])
            painter.drawText(projected_axes[i], labels[i])

class RobotGUIController(Node):
    def __init__(self):
        super().__init__('robot_gui_controller')
        
        # ROS2 Publishers and Subscribers
        self.cmd_vel_pub = self.create_publisher(Twist, 'cmd_vel', 10)
        self.camera_sub = self.create_subscription(
            Image, 
            'camera/image_raw', 
            self.camera_callback, 
            10
        )
        self.imu_sub = self.create_subscription(
            Imu,
            'imu/data',
            self.imu_callback,
            10
        )
        
        # Image processing bridge
        self.bridge = CvBridge()
        self.current_frame = None
        
        # IMU data
        self.acceleration = [0.0, 0.0, 0.0]
        self.angular_velocity = [0.0, 0.0, 0.0]
        self.orientation = [0.0, 0.0, 0.0, 1.0]
        self.heading = 0.0
        
        # Demo mode variables
        self.demo_mode = False
        self.demo_timer = 0
        self.demo_direction = 1
        
        # GUI setup
        self.app = QApplication([])
        self.window = QMainWindow()
        self.window.setWindowTitle('ROS2 Robot Controller')
        
        # Set to full screen
        screen = self.app.primaryScreen()
        screen_geometry = screen.geometry()
        self.window.setGeometry(screen_geometry)
        
        self.central_widget = QWidget()
        self.window.setCentralWidget(self.central_widget)
        
        self.main_layout = QGridLayout()
        self.central_widget.setLayout(self.main_layout)
        
        # Camera display
        self.camera_group = QGroupBox("Camera Feed")
        self.camera_layout = QVBoxLayout()
        self.camera_label = QLabel()
        self.camera_label.setAlignment(Qt.AlignCenter)
        self.camera_layout.addWidget(self.camera_label)
        self.camera_group.setLayout(self.camera_layout)
        
        # Control panel
        self.control_group = QGroupBox("Robot Control")
        self.control_layout = QVBoxLayout()
        
        # Demo mode checkbox
        self.demo_checkbox = QCheckBox("Demo Mode")
        self.demo_checkbox.stateChanged.connect(self.toggle_demo_mode)
        self.control_layout.addWidget(self.demo_checkbox)
        
        # Speed control
        self.speed_slider = QSlider(Qt.Horizontal)
        self.speed_slider.setRange(0, 100)
        self.speed_slider.setValue(50)
        self.speed_label = QLabel("Speed: 50%")
        self.control_layout.addWidget(self.speed_label)
        self.control_layout.addWidget(self.speed_slider)
        
        # Steering control
        self.steer_slider = QSlider(Qt.Horizontal)
        self.steer_slider.setRange(-100, 100)
        self.steer_slider.setValue(0)
        self.steer_label = QLabel("Steering: 0%")
        self.control_layout.addWidget(self.steer_label)
        self.control_layout.addWidget(self.steer_slider)
        
        # Buttons
        self.btn_layout = QHBoxLayout()
        self.emergency_btn = QPushButton("STOP")
        self.emergency_btn.setStyleSheet("background-color: red; color: white;")
        self.btn_layout.addWidget(self.emergency_btn)
        self.control_layout.addLayout(self.btn_layout)
        
        self.control_group.setLayout(self.control_layout)
        
        # Visualization panel
        self.viz_group = QGroupBox("Visualization")
        self.viz_layout = QHBoxLayout()
        
        # Robot visualization
        self.robot_viz_group = QGroupBox("Robot Orientation")
        self.robot_viz_layout = QVBoxLayout()
        self.robot_viz_label = QLabel()
        self.robot_viz_label.setAlignment(Qt.AlignCenter)
        self.robot_viz_label.setStyleSheet("background-color: black;")
        self.robot_viz_layout.addWidget(self.robot_viz_label)
        self.robot_viz_group.setLayout(self.robot_viz_layout)
        
        # Compass visualization
        self.compass_group = QGroupBox("Compass")
        self.compass_layout = QVBoxLayout()
        self.compass_widget = CompassWidget()
        self.compass_layout.addWidget(self.compass_widget)
        self.compass_group.setLayout(self.compass_layout)
        
        self.viz_layout.addWidget(self.robot_viz_group)
        self.viz_layout.addWidget(self.compass_group)
        self.viz_group.setLayout(self.viz_layout)
        
        # 3D Orientation visualization
        self.robot_3d_group = QGroupBox("3D Orientation")
        self.robot_3d_layout = QVBoxLayout()
        self.robot_3d_widget = Robot3DWidget()
        self.robot_3d_layout.addWidget(self.robot_3d_widget)
        self.robot_3d_group.setLayout(self.robot_3d_layout)
        
        # IMU data display
        self.imu_data_label = QLabel()
        self.imu_data_label.setAlignment(Qt.AlignLeft)
        self.imu_data_label.setFont(QFont('Arial', 10))
        self.imu_data_label.setStyleSheet("background-color: white; padding: 5px;")
        
        # Parameters display
        self.params_group = QGroupBox("Parameters")
        self.params_layout = QVBoxLayout()
        self.params_label = QLabel()
        self.params_label.setAlignment(Qt.AlignLeft)
        self.params_label.setFont(QFont('Arial', 10))
        self.params_layout.addWidget(self.params_label)
        self.params_group.setLayout(self.params_layout)
        
        # Add widgets to main layout
        self.main_layout.addWidget(self.camera_group, 0, 0, 2, 2)
        self.main_layout.addWidget(self.control_group, 0, 2, 1, 1)
        self.main_layout.addWidget(self.viz_group, 1, 2, 1, 1)
        self.main_layout.addWidget(self.imu_data_label, 2, 0, 1, 2)
        self.main_layout.addWidget(self.params_group, 2, 2, 1, 1)
        self.main_layout.addWidget(self.robot_3d_group, 3, 0, 1, 3)
        
        # Set column stretch factors
        self.main_layout.setColumnStretch(0, 3)
        self.main_layout.setColumnStretch(1, 3)
        self.main_layout.setColumnStretch(2, 2)
        
        # Connect signals
        self.speed_slider.valueChanged.connect(self.update_speed)
        self.steer_slider.valueChanged.connect(self.update_steering)
        self.emergency_btn.clicked.connect(self.emergency_stop)
        
        # Timer for updating GUI and sending commands
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_gui)
        self.timer.start(100)  # 10 Hz
        
        # Control variables
        self.linear_speed = 0.0
        self.angular_speed = 0.0
        self.max_linear_speed = 2.0  # m/s
        self.max_angular_speed = 1.5  # rad/s
        
        # Robot icon properties
        self.robot_size = 40
        self.robot_color = QColor(0, 255, 255)  # Cyan
        self.robot_direction = 0  # Angle in degrees
        
        self.window.showMaximized()
        
    def camera_callback(self, msg):
        try:
            cv_image = self.bridge.imgmsg_to_cv2(msg, "bgr8")
            self.current_frame = cv_image
        except Exception as e:
            self.get_logger().error(f"Error processing image: {str(e)}")
    
    def imu_callback(self, msg):
        # Update IMU data
        self.acceleration = [
            msg.linear_acceleration.x,
            msg.linear_acceleration.y,
            msg.linear_acceleration.z
        ]
        self.angular_velocity = [
            msg.angular_velocity.x,
            msg.angular_velocity.y,
            msg.angular_velocity.z
        ]
        self.orientation = [
            msg.orientation.x,
            msg.orientation.y,
            msg.orientation.z,
            msg.orientation.w
        ]
        
        # Calculate heading from quaternion
        if not self.demo_mode:
            t0 = +2.0 * (self.orientation[3] * self.orientation[0] + self.orientation[1] * self.orientation[2])
            t1 = +1.0 - 2.0 * (self.orientation[0] * self.orientation[0] + self.orientation[1] * self.orientation[1])
            new_heading = math.degrees(math.atan2(t0, t1)) % 360
            if abs(new_heading - self.heading) > 1:
                self.heading = new_heading
                self.compass_widget.set_target_heading(self.heading)
                self.robot_direction = self.heading
        
        # Calculate roll, pitch, yaw from quaternion
        x, y, z, w = self.orientation
        
        # Roll (X-axis rotation)
        sinr_cosp = 2 * (w * x + y * z)
        cosr_cosp = 1 - 2 * (x * x + y * y)
        roll = math.atan2(sinr_cosp, cosr_cosp)
        
        # Pitch (Y-axis rotation)
        sinp = 2 * (w * y - z * x)
        if abs(sinp) >= 1:
            pitch = math.copysign(math.pi / 2, sinp)
        else:
            pitch = math.asin(sinp)
        
        # Yaw (Z-axis rotation)
        siny_cosp = 2 * (w * z + x * y)
        cosy_cosp = 1 - 2 * (y * y + z * z)
        yaw = math.atan2(siny_cosp, cosy_cosp)
        
        # Update 3D visualization
        self.robot_3d_widget.set_orientation(roll, pitch, yaw)
    
    def toggle_demo_mode(self, state):
        self.demo_mode = state == Qt.Checked
        if self.demo_mode:
            self.demo_timer = 0
            self.get_logger().info("Demo mode activated")
        else:
            self.get_logger().info("Demo mode deactivated")
    
    def update_gui(self):
        self.update_camera_display()
        self.update_robot_visualization()
        self.update_imu_data_display()
        self.update_params_display()
        
        if self.demo_mode:
            self.update_demo_mode()
        else:
            self.send_robot_command(self.linear_speed, self.angular_speed)
    
    def update_demo_mode(self):
        self.demo_timer += 1
        
        # Update robot direction in demo mode
        new_heading = (self.robot_direction + 2) % 360
        if abs(new_heading - self.heading) > 1:
            self.heading = new_heading
            self.compass_widget.set_target_heading(self.heading)
            self.robot_direction = self.heading
        
        # Oscillate between forward and backward movement
        if self.demo_timer % 50 == 0:
            self.demo_direction *= -1
        
        # Calculate demo speeds
        demo_speed = 0.5 * self.max_linear_speed * self.demo_direction
        demo_steering = 0.3 * self.max_angular_speed * math.sin(self.demo_timer * 0.1)
        
        # Update sliders for visualization
        speed_percent = int((demo_speed / self.max_linear_speed) * 100)
        steer_percent = int((demo_steering / self.max_angular_speed) * 100)
        
        self.speed_slider.setValue(abs(speed_percent))
        self.steer_slider.setValue(steer_percent)
        
        self.send_robot_command(demo_speed, demo_steering)
    
    def update_camera_display(self):
        if self.current_frame is not None:
            rgb_image = cv2.cvtColor(self.current_frame, cv2.COLOR_BGR2RGB)
            h, w, ch = rgb_image.shape
            bytes_per_line = ch * w
            qt_image = QImage(
                rgb_image.data, w, h, bytes_per_line, QImage.Format_RGB888
            )
            
            scaled_pixmap = QPixmap.fromImage(qt_image).scaled(
                self.camera_label.width(),
                self.camera_label.height(),
                Qt.KeepAspectRatio,
                Qt.SmoothTransformation
            )
            self.camera_label.setPixmap(scaled_pixmap)
    
    def update_robot_visualization(self):
        viz_pixmap = QPixmap(self.robot_viz_label.width(), self.robot_viz_label.height())
        viz_pixmap.fill(Qt.black)
        
        painter = QPainter(viz_pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        center_x = viz_pixmap.width() // 2
        center_y = viz_pixmap.height() // 2
        radius = min(viz_pixmap.width(), viz_pixmap.height()) // 3
        
        # Draw axes
        painter.setPen(QPen(Qt.white, 1, Qt.DashLine))
        painter.drawLine(center_x - radius, center_y, center_x + radius, center_y)
        painter.drawLine(center_x, center_y - radius, center_x, center_y + radius)
        
        # Draw robot icon
        robot_center = QPoint(center_x, center_y)
        self.draw_robot(painter, robot_center, self.robot_direction)
        
        # Draw acceleration vector
        accel_scale = radius / 10.0
        accel_x = center_x + int(self.acceleration[0] * accel_scale)
        accel_y = center_y - int(self.acceleration[1] * accel_scale)
        
        painter.setPen(QPen(Qt.yellow, 2))
        painter.drawLine(center_x, center_y, accel_x, accel_y)
        painter.drawEllipse(accel_x - 3, accel_y - 3, 6, 6)
        
        painter.end()
        self.robot_viz_label.setPixmap(viz_pixmap)
    
    def draw_robot(self, painter, center, angle_deg):
        size = self.robot_size
        angle_rad = math.radians(angle_deg)
        
        points = [
            QPointF(
                center.x() + size * math.cos(angle_rad),
                center.y() - size * math.sin(angle_rad)
            ),
            QPointF(
                center.x() + size/2 * math.cos(angle_rad + 2.1),
                center.y() - size/2 * math.sin(angle_rad + 2.1)
            ),
            QPointF(
                center.x() + size/2 * math.cos(angle_rad - 2.1),
                center.y() - size/2 * math.sin(angle_rad - 2.1)
            )
        ]
        
        painter.setBrush(self.robot_color)
        painter.setPen(QPen(Qt.black, 2))
        painter.drawPolygon(QPolygon([p.toPoint() for p in points]))
        
        eye_size = size / 5
        eye_pos = QPointF(
            center.x() + (size + eye_size) * math.cos(angle_rad),
            center.y() - (size + eye_size) * math.sin(angle_rad)
        )
        
        painter.setBrush(Qt.white)
        painter.drawEllipse(eye_pos.toPoint(), int(eye_size), int(eye_size))
        painter.setBrush(Qt.black)
        painter.drawEllipse(eye_pos.toPoint(), int(eye_size/2), int(eye_size/2))
    
    def update_imu_data_display(self):
        imu_text = (
            f"Acceleration (m/s²):\n"
            f"  X: {self.acceleration[0]:.2f}\n"
            f"  Y: {self.acceleration[1]:.2f}\n"
            f"  Z: {self.acceleration[2]:.2f}\n\n"
            f"Angular Velocity (rad/s):\n"
            f"  X: {self.angular_velocity[0]:.2f}\n"
            f"  Y: {self.angular_velocity[1]:.2f}\n"
            f"  Z: {self.angular_velocity[2]:.2f}\n\n"
            f"Orientation (quaternion):\n"
            f"  X: {self.orientation[0]:.2f}\n"
            f"  Y: {self.orientation[1]:.2f}\n"
            f"  Z: {self.orientation[2]:.2f}\n"
            f"  W: {self.orientation[3]:.2f}"
        )
        self.imu_data_label.setText(imu_text)
    
    def update_params_display(self):
        mode_text = "DEMO MODE" if self.demo_mode else "MANUAL MODE"
        params_text = (
            f"Current Parameters ({mode_text}):\n"
            f"Linear Speed: {self.linear_speed:.2f} m/s\n"
            f"Angular Speed: {self.angular_speed:.2f} rad/s\n"
            f"Max Linear Speed: {self.max_linear_speed:.2f} m/s\n"
            f"Max Angular Speed: {self.max_angular_speed:.2f} rad/s\n"
            f"Robot Direction: {self.robot_direction:.1f}°\n"
            f"Compass Heading: {int(self.heading)}°"
        )
        self.params_label.setText(params_text)
    
    def update_speed(self, value):
        self.speed_label.setText(f"Speed: {value}%")
        self.linear_speed = (value / 100.0) * self.max_linear_speed
    
    def update_steering(self, value):
        self.steer_label.setText(f"Steering: {value}%")
        self.angular_speed = (value / 100.0) * self.max_angular_speed
    
    def emergency_stop(self):
        self.speed_slider.setValue(0)
        self.steer_slider.setValue(0)
        self.send_robot_command(0.0, 0.0)
    
    def send_robot_command(self, linear, angular):
        msg = Twist()
        msg.linear.x = linear
        msg.angular.z = angular
        self.cmd_vel_pub.publish(msg)
    
    def run(self):
        self.app.exec_()

def main(args=None):
    rclpy.init(args=args)
    gui_controller = RobotGUIController()
    
    try:
        gui_controller.run()
    except KeyboardInterrupt:
        pass
    finally:
        gui_controller.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()