const { app, BrowserWindow } = require('electron');
const path = require('node:path');
app.disableHardwareAcceleration()

// Handle creating/removing shortcuts on Windows when installing/uninstalling.
if (require('electron-squirrel-startup')) {
  app.quit();
}

const createWindow = async () => {
  app.commandLine.appendSwitch('--no-sandbox')
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 1024,
    height: 600,
    movable: false,
    resizable: false,
    maximizable: false,
    minimizable: false,
    titleBarStyle: 'hidden',
    webPreferences: {
      webSecurity: false,
      preload: path.join(__dirname, 'preload.js'),
      DevTools:true,
    },
  });
  // and load the index.html of the app.
  // mainWindow.setRepresentedFilename(path.join(__dirname,'../html'))
  // mainWindow.loadFile(path.join(__dirname, '../html/index.html'));
  mainWindow.webContents.openDevTools();
  
  mainWindow.loadURL('http://localhost:5173')

  mainWindow.maximize();

};
// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(() => {
  createWindow();

  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});
