// See the Electron documentation for details on how to use preload scripts:
// https://www.electronjs.org/docs/latest/tutorial/process-model#preload-scripts
const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// contextBridge.exposeInMainWorld('electronAPI', {
//   setVelocity: (linear, angular) => ipcRenderer.send('set-velocity', { linear, angular }),
//   onCameraUpdate: (callback) => ipcRenderer.on('camera-update', callback),
//   onImuUpdate: (callback) => ipcRenderer.on('imu-update', callback)
// });