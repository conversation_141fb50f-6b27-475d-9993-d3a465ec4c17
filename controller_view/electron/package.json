{"name": "electron", "productName": "electron", "version": "1.0.0", "description": "My Electron application description", "main": "src/index.js", "scripts": {"start": "electron-forge start -- --no-sandbox --enable-unsafe-swiftshader", "package": "electron-forge package", "make": "electron-forge make", "publish": "electron-forge publish", "lint": "echo \"No linting configured\""}, "keywords": [], "author": "root", "license": "MIT", "dependencies": {"electron-squirrel-startup": "^1.0.1", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/preset-react": "^7.27.1", "@electron-forge/cli": "^7.8.1", "@electron-forge/maker-deb": "^7.8.1", "@electron-forge/maker-rpm": "^7.8.1", "@electron-forge/maker-squirrel": "^7.8.1", "@electron-forge/maker-zip": "^7.8.1", "@electron-forge/plugin-auto-unpack-natives": "^7.8.1", "@electron-forge/plugin-fuses": "^7.8.1", "@electron-forge/plugin-webpack": "^7.8.1", "@electron/fuses": "^1.8.0", "babel-loader": "^10.0.0", "electron": "^36.4.0", "electron-builder": "^26.0.12"}}