import { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';

export default function Joystick({ position, onMove, onStop }) {
  const joystickAreaRef = useRef(null);
  const [isDragging, setIsDragging] = useState(false);

  const getEventPosition = (e) => {
    if (e.touches && e.touches[0]) {
      return {
        clientX: e.touches[0].clientX,
        clientY: e.touches[0].clientY
      };
    }
    return {
      clientX: e.clientX,
      clientY: e.clientY
    };
  };

  const handleJoystickMove = (e) => {
    if (!isDragging) return;

    const { clientX, clientY } = getEventPosition(e);
    const joystickArea = joystickAreaRef.current.getBoundingClientRect();
    const centerX = joystickArea.width / 2;
    const centerY = joystickArea.height / 2;

    let x = clientX - joystickArea.left - centerX;
    let y = clientY - joystickArea.top - centerY;

    const distance = Math.sqrt(x * x + y * y);
    const maxDistance = centerX - 30;

    if (distance > maxDistance) {
      x = (x / distance) * maxDistance;
      y = (y / distance) * maxDistance;
    }

    onMove(x / maxDistance, y / maxDistance);
  };

  const startDragging = (e) => {
    e.preventDefault();
    setIsDragging(true);

    document.addEventListener('mousemove', handleJoystickMove);
    document.addEventListener('mouseup', stopDragging);
    document.addEventListener('touchmove', handleJoystickMove, { passive: false });
    document.addEventListener('touchend', stopDragging);
  };

  const stopDragging = () => {
    setIsDragging(false);
    onStop();

    document.removeEventListener('mousemove', handleJoystickMove);
    document.removeEventListener('mouseup', stopDragging);
    document.removeEventListener('touchmove', handleJoystickMove);
    document.removeEventListener('touchend', stopDragging);
  };

  useEffect(() => {
    return () => {
      stopDragging();
    };
  }, []);

  return (
    <motion.div
      ref={joystickAreaRef}
      className="relative w-full h-64 bg-gray-800 rounded-full flex items-center justify-center touch-none"
      onMouseDown={startDragging}
      onTouchStart={startDragging}
      whileTap={{ scale: 0.95 }}
    >
      <motion.div
        className="absolute w-16 h-16 bg-blue-600 rounded-full border-2 border-blue-400 cursor-pointer touch-none"
        style={{
          left: '50%',
          top: '50%',
          marginLeft: '-32px',
          marginTop: '-32px'
        }}
        animate={{
          x: position.x * 100,
          y: position.y * 100,
          backgroundColor: isDragging ? '#3b82f6' : '#2563eb'
        }}
        transition={{ type: 'spring', damping: 10, stiffness: 100 }}
      />
      
      {/* Kierunki */}
      <div className="absolute top-2 left-1/2 transform -translate-x-1/2 text-xs text-gray-400">FWD</div>
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 text-xs text-gray-400">REV</div>
      <div className="absolute left-2 top-1/2 transform -translate-y-1/2 text-xs text-gray-400">LEFT</div>
      <div className="absolute right-2 top-1/2 transform -translate-y-1/2 text-xs text-gray-400">RIGHT</div>
    </motion.div>
  );
}
