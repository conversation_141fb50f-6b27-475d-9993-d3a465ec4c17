import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';

const tabs = [
  { id: 'control', label: 'Sterowanie' },
  { id: 'sensors', label: 'Sensory' },
  { id: 'camera', label: '<PERSON><PERSON><PERSON>' },
  { id: 'calibration', label: '<PERSON><PERSON><PERSON><PERSON>' },
  { id: 'configuration', label: 'Konfiguracja' }
];

// Funkcja do przeliczania napięcia na procent dla baterii 3S LiPo
const calculateBatteryPercentage = (voltage) => {
  // Zakres napięć dla baterii 3S LiPo:
  const minVoltage = 9.0;  // 0%
  const maxVoltage = 12.6; // 100%
  
  // Ograniczenie wartości do zakresu
  const clampedVoltage = Math.max(minVoltage, Math.min(voltage, maxVoltage));
  
  // Przeliczenie na procenty
  const percentage = ((clampedVoltage - minVoltage) / (maxVoltage - minVoltage)) * 100;
  
  return Math.round(percentage);
};

export default function Toolbar({
  activeTab,
  setActiveTab,
  isConnected,
}) {
  const [powerData, setPowerData] = useState({
    voltage: 0,
    current: 0,
    updatedAt: ''
  });

  const [batteryPercentage, setBatteryPercentage] = useState(0);

  useEffect(() => {
    const fetchPowerData = async () => {
      if (!isConnected) return;

      try {
        const response = await fetch('http://localhost:3000/api/power');
        const data = await response.json();

        if (data.success) {
          const newPowerData = {
            voltage: data.data.voltage,
            current: data.data.current,
            updatedAt: new Date(data.data.updatedAt).toLocaleTimeString()
          };
          setPowerData(newPowerData);
          setBatteryPercentage(calculateBatteryPercentage(newPowerData.voltage));
        }
      } catch (error) {
        console.error('Error fetching power data:', error);
      }
    };

    // Fetch immediately and then every 5 seconds
    fetchPowerData();
    const interval = setInterval(fetchPowerData, 5000);

    return () => clearInterval(interval);
  }, [isConnected]);

  return (
    <div className="bg-gray-800 p-2 flex items-center justify-between border-b border-gray-700">
      <div className="flex space-x-2">
        {tabs.map((tab) => (
          <motion.button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`px-4 py-2 rounded-lg relative ${activeTab === tab.id ? 'bg-blue-600' : 'bg-gray-700'}`}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {tab.label}
            {activeTab === tab.id && (
              <motion.div
                layoutId="tabIndicator"
                className="absolute bottom-0 left-0 w-full h-1 bg-blue-400 rounded-b"
                transition={{ type: 'spring', bounce: 0.2, duration: 0.6 }}
              />
            )}
          </motion.button>
        ))}
      </div>

      <div className="flex items-center space-x-4">
        <motion.div
          className="px-3 py-1 rounded-lg bg-gray-700 flex items-center space-x-2"
          whileHover={{ scale: 1.05 }}
        >
          <span className="text-xs text-gray-400">Zasilanie:</span>
          <span className="text-green-400">{powerData.voltage !=null ?powerData.voltage.toFixed(2) : 'N/A '}V</span>
          <span className="text-blue-400">{powerData.current !=null ?powerData.current.toFixed(2) : 'N/A '}A</span>
          <span className="text-xs text-gray-400">{powerData.updatedAt}</span>
        </motion.div>

        <motion.div className="flex items-center" whileHover={{ scale: 1.05 }}>
          <span className="mr-2">{batteryPercentage}%</span>
          <div className="w-20 h-4 bg-gray-700 rounded-full overflow-hidden">
            <motion.div
              className={`h-full ${batteryPercentage > 30 ? 'bg-green-500' : 'bg-red-500'}`}
              initial={{ width: 0 }}
              animate={{ width: `${batteryPercentage}%` }}
              transition={{ duration: 1 }}
            />
          </div>
        </motion.div>
      </div>
    </div>
  );
}