import { motion } from 'framer-motion';
import Joystick from '../shared/Joystick';
import { useState } from 'react';

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.5
    }
  }
};

export default function ControlPanel({
  linearSpeed,
  angularSpeed,
  heading,
  onLinearSpeedChange,
  onAngularSpeedChange,
}) {
  const [leftJoystick, setLeftJoystick] = useState({ x: 0, y: 0 });
  const [rightJoystick, setRightJoystick] = useState({ x: 0, y: 0 });

  const handleLeftMove = (x, y) => {
    setLeftJoystick({ x, y });
    onLinearSpeedChange((-y).toFixed(2));
  };

  const handleLeftStop = () => {
    setLeftJoystick({ x: 0, y: 0 });
    onLinearSpeedChange(0);
  };

  const handleRightMove = (x, y) => {
    setRightJoystick({ x, y });
    onAngularSpeedChange((x).toFixed(2));
  };

  const handleRightStop = () => {
    setRightJoystick({ x: 0, y: 0 });
    onAngularSpeedChange(0);
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="p-4 grid grid-cols-3 gap-4 items-center justify-center h-full"
    >
      {/* Lewy joystick */}
      <motion.div variants={itemVariants} className="flex flex-col items-center">
        <h3 className="text-lg font-semibold mb-2">Gaz / Wsteczny</h3>
        <Joystick
          position={leftJoystick}
          onMove={handleLeftMove}
          onStop={handleLeftStop}
        />
      </motion.div>

      {/* Wykres pionowy */}
      <motion.div
        variants={itemVariants}
        className="relative flex flex-col items-center justify-center h-full"
      >
        <h3 className="text-lg font-semibold mb-2">Moc</h3>
        <div className="w-8 h-64 bg-gray-800 rounded-full border border-gray-700 relative overflow-hidden">
          <div
            className={`absolute bottom-1/2 left-0 right-0 bg-purple-600`}
            style={{
              height: `${Math.abs(leftJoystick.y) * 50}%`,
              top: leftJoystick.y < 0 ? '0' : undefined,
              bottom: leftJoystick.y > 0 ? '0' : undefined
            }}
          />
        </div>
        <div className="mt-2 text-sm text-gray-400">{(-leftJoystick.y).toFixed(2)} m/s</div>
      </motion.div>

      {/* Prawy joystick */}
      <motion.div variants={itemVariants} className="flex flex-col items-center">
        <h3 className="text-lg font-semibold mb-2">Skręt</h3>
        <Joystick
          position={rightJoystick}
          onMove={handleRightMove}
          onStop={handleRightStop}
        />
      </motion.div>
    </motion.div>
  );
}
