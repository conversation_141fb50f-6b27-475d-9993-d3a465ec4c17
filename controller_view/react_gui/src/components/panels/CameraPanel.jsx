export default function CameraPanel() {
  return (
    <div className="bg-gray-900 rounded-xl p-4 border border-gray-700">
      <h3 className="text-lg font-semibold mb-2">Podgląd kamery</h3>
      <div className="bg-black rounded-lg h-96 flex items-center justify-center">
        <div className="text-center text-gray-500">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
          </svg>
          Podgląd kamery niedostępny
        </div>
      </div>
      <div className="flex justify-center mt-4 space-x-4">
        <button className="px-4 py-2 bg-gray-700 rounded-lg">Przednia kamera</button>
        <button className="px-4 py-2 bg-gray-700 rounded-lg">Tylna kamera</button>
      </div>
    </div>
  );
}