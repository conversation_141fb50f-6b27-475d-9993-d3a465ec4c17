export default function CalibrationPanel({ calibration, onCalibrationChange }) {
  return (
    <div className="p-4 grid grid-cols-1 md:grid-cols-2 gap-4">
      {/* Postęp kalibracji */}
      <div className="bg-gray-900 rounded-xl p-4 border border-gray-700">
        <h3 className="text-lg font-semibold mb-4">Postęp kalibracji</h3>
        <div className="space-y-6">
          <div>
            <div className="flex justify-between mb-1">
              <span className="text-gray-400">IMU</span>
              <span className="font-mono">{calibration.imuProgress}%</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2.5">
              <div
                className="bg-blue-600 h-2.5 rounded-full"
                style={{ width: `${calibration.imuProgress}%` }}
              ></div>
            </div>
          </div>

          <div>
            <div className="flex justify-between mb-1">
              <span className="text-gray-400">Silniki</span>
              <span className="font-mono">{calibration.motorsProgress}%</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2.5">
              <div
                className="bg-green-500 h-2.5 rounded-full"
                style={{ width: `${calibration.motorsProgress}%` }}
              ></div>
            </div>
          </div>

          <div>
            <div className="flex justify-between mb-1">
              <span className="text-gray-400">Sensory</span>
              <span className="font-mono">{calibration.sensorsProgress}%</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2.5">
              <div
                className="bg-purple-500 h-2.5 rounded-full"
                style={{ width: `${calibration.sensorsProgress}%` }}
              ></div>
            </div>
          </div>
        </div>

        <div className="mt-6 space-y-2">
          <button className="w-full py-2 bg-blue-600 rounded-lg">
            Rozpocznij kalibrację
          </button>
          <button className="w-full py-2 bg-gray-700 rounded-lg">
            Kalibracja automatyczna
          </button>
        </div>
      </div>

      {/* Instrukcje kalibracji */}
      <div className="bg-gray-900 rounded-xl p-4 border border-gray-700">
        <h3 className="text-lg font-semibold mb-4">Instrukcje kalibracji</h3>
        <div className="prose prose-invert text-gray-300">
          <ol className="list-decimal pl-5 space-y-2">
            <li>Upewnij się, że robot znajduje się na płaskiej powierzchni</li>
            <li>Zatrzymaj wszystkie ruchome części robota</li>
            <li>Podłącz zasilanie i upewnij się, że bateria jest naładowana</li>
            <li>Kliknij "Rozpocznij kalibrację"</li>
            <li>Nie poruszaj robotem podczas procesu kalibracji</li>
            <li>Postępuj zgodnie z instrukcjami wyświetlanymi na ekranie</li>
          </ol>

          <div className="mt-4 p-3 bg-yellow-900 bg-opacity-30 rounded-lg border border-yellow-700">
            <strong>Uwaga:</strong> Proces kalibracji może zająć kilka minut. Nie wyłączaj zasilania podczas kalibracji.
          </div>
        </div>
      </div>
    </div>
  );
}