import { useEffect, useState } from "react";
import TemperaturePanel from "./sensors/Temp";
import IMUPanel from "./sensors/IMU";
import GyroVisualization from "./sensors/Gyro";
import Compass from "./sensors/Compass";

export default function SensorsPanel({ imuData, heading }) {
  const [motorTemperatures, setMotorTemperatures] = useState({
    motorLeft: 0,
    motorRight: 0
  });


  const [gyroData, setGyroData] = useState({
    accelerometer: { x: 0, y: 0, z: 0, unit: 'm/s²' },
    gyroscope: { x: 0, y: 0, z: 0, unit: '°/s' },
    dataQuality: 'unknown'
  });

  const [tempHistory, setTempHistory] = useState({
    motorLeft: [],
    motorRight: []
  });

  // Funkcje fetchujące dane pozostają tutaj, ponieważ zarządzają stanem
  const fetchTemperatures = async () => {
    try {
      const response = await fetch('http://localhost:3000/api/temperatures');
      if (!response.ok) throw new Error(`Błąd HTTP! status: ${response.status}`);
      const data = await response.json();
      
      const leftTemp = data.temperature_1 ?? data.data?.temperature_1 ?? 0;
      const rightTemp = data.temperature_2 ?? data.data?.temperature_2 ?? 0;

      setMotorTemperatures({
        motorLeft: parseFloat(leftTemp),
        motorRight: parseFloat(rightTemp)
      });

      setTempHistory(prev => {
        const now = new Date();
        return {
          motorLeft: [...prev.motorLeft, { x: now, y: parseFloat(leftTemp) }].slice(-30),
          motorRight: [...prev.motorRight, { x: now, y: parseFloat(rightTemp) }].slice(-30)
        };
      });
    } catch (error) {
      console.error('Błąd pobierania temperatur:', error);
      setMotorTemperatures({
        motorLeft: parseFloat(0),
        motorRight: parseFloat(0)
      });
    }
  };

  const fetchGyroData = async () => {
    try {
      const response = await fetch('http://localhost:3000/api/gyro');
      if (!response.ok) throw new Error(`Błąd HTTP! status: ${response.status}`);
      const data = await response.json();

      if (data.success && data.data) {
        setGyroData({
          accelerometer: {
            x: parseFloat(data.data.accelerometer.x),
            y: parseFloat(data.data.accelerometer.y),
            z: parseFloat(data.data.accelerometer.z),
            unit: data.data.accelerometer.unit
          },
          gyroscope: {
            x: parseFloat(data.data.gyroscope.x),
            y: parseFloat(data.data.gyroscope.y),
            z: parseFloat(data.data.gyroscope.z),
            unit: data.data.gyroscope.unit
          },
          dataQuality: data.data.dataQuality || 'unknown'
        });
      }
    } catch (error) {
      console.error('Błąd pobierania danych żyroskopu:', error);
    }
  };

  useEffect(() => {
    fetchTemperatures();
    fetchGyroData();
    
    const tempIntervalId = setInterval(fetchTemperatures, 1000);
    const gyroIntervalId = setInterval(fetchGyroData, 500);
    
    return () => {
      clearInterval(tempIntervalId);
      clearInterval(gyroIntervalId);
    };
  }, []);

  return (
    <div className="p-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <IMUPanel imuData={imuData} gyroData={gyroData} />
      <TemperaturePanel 
        motorTemperatures={motorTemperatures} 
        tempHistory={tempHistory} 
      />
      <GyroVisualization gyroData={gyroData} />
      <Compass heading={heading}/>
    </div>
  );
}