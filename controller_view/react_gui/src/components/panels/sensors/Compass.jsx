import React from 'react'
import { motion } from 'framer-motion';

const Compass = ({
    heading
}) => {
    const itemVariants = {
        hidden: { y: 20, opacity: 0 },
        visible: {
            y: 0,
            opacity: 1,
            transition: {
                duration: 0.5
            }
        }
    };
    return (

        <motion.div
            variants={itemVariants}
            className="bg-gray-900 rounded-xl p-4 border border-gray-700"
        >
            <h3 className="text-lg font-semibold mb-4"><PERSON><PERSON><PERSON><PERSON></h3>
            <div className="flex flex-col items-center">
                <motion.div
                    className="relative w-40 h-40 mb-4"
                    animate={{ rotate: 360 }}
                    transition={{ duration: 10, repeat: Infinity, ease: "linear" }}
                >
                    <div className="absolute inset-0 rounded-full border-2 border-gray-700"></div>
                    <div className="absolute top-0 left-1/2 transform -translate-x-1/2 mt-1 text-red-500 font-bold">N</div>
                    <motion.div
                        className="absolute bottom-1/2 left-1/2 w-1 h-16 bg-red-500 transform -translate-x-1/2 origin-bottom"
                        initial={{ rotate: heading }}
                        animate={{ rotate: heading }}
                        transition={{ type: 'spring', damping: 10 }}
                    />
                </motion.div>
                <motion.div
                    className="text-2xl font-mono"
                    key={heading}
                    initial={{ scale: 1.5 }}
                    animate={{ scale: 1 }}
                    transition={{ duration: 0.5 }}
                >
                    {heading}°
                </motion.div>
            </div>
        </motion.div >
    )
}

export default Compass