export default function IMUPanel({ imuData, gyroData }) {
  return (
    <div className="bg-gray-900 rounded-xl p-4 border border-gray-700">
      <h3 className="text-lg font-semibold mb-4 text-white">Dane IMU i Żyroskop</h3>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <h4 className="text-gray-400 mb-2">Orientacja</h4>
          <div className="space-y-1">
            {Object.entries(imuData.orientation).map(([axis, value]) => (
              <div key={axis} className="flex justify-between">
                <span className="text-gray-300">{axis.toUpperCase()}:</span>
                <span className="font-mono text-white">{value.toFixed(4)}</span>
              </div>
            ))}
          </div>
        </div>

        <div>
          <h4 className="text-gray-400 mb-2">Przyspieszenie</h4>
          <div className="space-y-1">
            {Object.entries(gyroData.accelerometer)
              .filter(([key]) => key !== 'unit')
              .map(([axis, value]) => (
                <div key={axis} className="flex justify-between">
                  <span className="text-gray-300">{axis.toUpperCase()}:</span>
                  <span className="font-mono text-white">
                    {parseFloat(value).toFixed(4)} {gyroData.accelerometer.unit}
                  </span>
                </div>
              ))}
          </div>
        </div>
        
        <div className="col-span-2">
          <h4 className="text-gray-400 mb-2">Żyroskop</h4>
          <div className="grid grid-cols-3 gap-2">
            {Object.entries(gyroData.gyroscope)
              .filter(([key]) => key !== 'unit')
              .map(([axis, value]) => (
                <div key={axis} className="flex justify-between">
                  <span className="text-gray-300">{axis.toUpperCase()}:</span>
                  <span className="font-mono text-white">
                    {parseFloat(value).toFixed(2)} {gyroData.gyroscope.unit}
                  </span>
                </div>
              ))}
          </div>
          <div className="mt-2 text-sm text-gray-400">
            Jakość danych: <span className="capitalize">{gyroData.dataQuality}</span>
          </div>
        </div>
      </div>
    </div>
  );
}