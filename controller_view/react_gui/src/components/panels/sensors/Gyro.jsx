import { useEffect, useRef, useState } from 'react';
import * as THREE from 'three';

export default function GyroVisualization({ gyroData }) {
  const mountRef = useRef(null);
  const rendererRef = useRef(null);
  const sceneRef = useRef(null);
  const cubeRef = useRef(null);
  const animationFrameId = useRef(null);
  const [webGLError, setWebGLError] = useState(null);

  // Inicjalizacja sceny tylko raz
  useEffect(() => {
    if (!mountRef.current) return;

    try {
      const canvas = document.createElement('canvas');
      const webglSupported = !!(
        window.WebGLRenderingContext &&
        (canvas.getContext('webgl') || canvas.getContext('experimental-webgl'))
      );

      if (!webglSupported) {
        throw new Error('WebGL nie jest wspierany w tej przeglądarce');
      }

      // Inicjalizacja sceny
      const scene = new THREE.Scene();
      sceneRef.current = scene;

      const camera = new THREE.PerspectiveCamera(75, 1, 0.1, 1000);
      camera.position.z = 3;

      const renderer = new THREE.WebGLRenderer({
        antialias: true,
        alpha: true,
        powerPreference: 'low-power'
      });

      renderer.setSize(300, 300);
      mountRef.current.appendChild(renderer.domElement);
      rendererRef.current = renderer;

      // Światło
      const light = new THREE.DirectionalLight(0xffffff, 1);
      light.position.set(1, 1, 1);
      scene.add(light);
      scene.add(new THREE.AmbientLight(0x404040));

      // Kostka
      const cubeGeometry = new THREE.BoxGeometry(1, 1, 1);
      const cubeMaterial = new THREE.MeshPhongMaterial({
        color: 0x00aaff,
        transparent: true,
        opacity: 0.7
      });

      const cube = new THREE.Mesh(cubeGeometry, cubeMaterial);
      scene.add(cube);
      cubeRef.current = cube;

      // Osie pomocnicze
      scene.add(new THREE.AxesHelper(2));

      const animate = () => {
        animationFrameId.current = requestAnimationFrame(animate);
        renderer.render(scene, camera);
      };

      animate();
      setWebGLError(null);
    } catch (error) {
      console.error('Błąd wizualizacji 3D:', error);
      setWebGLError(error.message);
    }

    // Czyszczenie przy odmontowaniu
    return () => {
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current);
      }

      if (rendererRef.current) {
        rendererRef.current.dispose();
        if (rendererRef.current.domElement && mountRef.current?.contains(rendererRef.current.domElement)) {
          mountRef.current.removeChild(rendererRef.current.domElement);
        }
      }

      rendererRef.current = null;
      sceneRef.current = null;
      cubeRef.current = null;
    };
  }, []);

  // Aktualizacja rotacji
  useEffect(() => {
    if (!cubeRef.current || !gyroData?.gyroscope) return;

    const { x, y, z } = gyroData.gyroscope;
    cubeRef.current.rotation.x += THREE.MathUtils.degToRad(x) * 0.01;
    cubeRef.current.rotation.y += THREE.MathUtils.degToRad(y) * 0.01;
    cubeRef.current.rotation.z += THREE.MathUtils.degToRad(z) * 0.01;
  }, [gyroData]);

  return (
    <div className="bg-gray-900 rounded-xl p-4 border border-gray-700">
      <h3 className="text-lg font-semibold mb-4 text-white">Wizualizacja 3D żyroskopu</h3>
      <div className="bg-gray-800 rounded-lg h-64 flex items-center justify-center">
        {webGLError ? (
          <div className="text-center text-gray-500 p-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto mb-2 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <p className="text-red-400 font-medium">Błąd WebGL: {webGLError}</p>
            <p className="text-sm mt-2">Wizualizacja 3D niedostępna. Sprawdź wsparcie przeglądarki dla WebGL.</p>
          </div>
        ) : (
          <div ref={mountRef} className="w-full h-full" />
        )}
      </div>
      <div className="mt-2 text-sm text-gray-400 text-center">
        {webGLError ? 'Użyj Chrome/Firefox z obsługą WebGL' : 'Obrót modelu w przestrzeni 3D zgodnie z danymi z żyroskopu'}
      </div>
    </div>
  );
}
