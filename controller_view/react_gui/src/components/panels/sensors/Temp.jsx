import { Chart } from 'react-chartjs-2';
import {
  Chart as Chart<PERSON><PERSON>,
  LineController,
  LineElement,
  PointElement,
  LinearScale,
  TimeScale,
  Title,
  Tooltip,
  Legend,
  CategoryScale
} from 'chart.js';
import 'chartjs-adapter-date-fns';
import { useMemo } from 'react';

ChartJS.register(
  LineController,
  LineElement,
  PointElement,
  LinearScale,
  TimeScale,
  Title,
  Tooltip,
  Legend,
  CategoryScale
);

export default function TemperaturePanel({ motorTemperatures, tempHistory }) {
  const calculateStats = (temps) => {
    if (!temps || temps.length === 0) return { min: 0, max: 0, avg: 0 };
    const values = temps.map(item => item.y);
    return {
      min: Math.min(...values),
      max: Math.max(...values),
      avg: values.reduce((a, b) => a + b, 0) / values.length
    };
  };

  const leftStats = calculateStats(tempHistory.motorLeft);
  const rightStats = calculateStats(tempHistory.motorRight);

  const chartData = useMemo(() => ({
    datasets: [
      {
        label: 'Silnik lewy',
        data: tempHistory.motorLeft,
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        fill: true
      },
      {
        label: 'Silnik prawy',
        data: tempHistory.motorRight,
        borderColor: 'rgb(239, 68, 68)',
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        fill: true
      }
    ]
  }), [tempHistory]);

  const chartOptions = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: {
        type: 'time',
        time: {
          unit: 'second',
          displayFormats: { second: 'HH:mm:ss' }
        },
        grid: { display: false },
        ticks: { color: '#9CA3AF' }
      },
      y: {
        min: 0,
        max: 40,
        title: {
          display: true,
          text: 'Temperatura (°C)',
          color: '#9CA3AF'
        },
        grid: { color: 'rgba(156, 163, 175, 0.1)' },
        ticks: { color: '#9CA3AF' }
      }
    },
    plugins: {
      legend: {
        position: 'top',
        labels: {
          color: '#F3F4F6',
          font: { size: 12 }
        }
      },
      tooltip: {
        backgroundColor: 'rgba(17, 24, 39, 0.9)',
        titleColor: '#F3F4F6',
        bodyColor: '#F3F4F6',
        borderColor: '#4B5563',
        borderWidth: 1,
        callbacks: {
          label: (context) => `${context.dataset.label}: ${context.parsed.y.toFixed(1)}°C`
        }
      }
    },
    elements: {
      line: { borderWidth: 2, tension: 0.1 },
      point: {
        radius: 3,
        borderWidth: 0,
        hoverRadius: 5,
        backgroundColor: 'rgba(255, 255, 255, 0.8)'
      }
    }
  }), []);

  return (
    <div className="bg-gray-900 rounded-xl p-4 border border-gray-700">
      <h3 className="text-lg font-semibold mb-4 text-white">Temperatury silników</h3>

      <div className="h-48 mb-4">
        <Chart
          type="line"
          data={chartData}
          options={chartOptions}
        />
      </div>

      <div className="grid grid-cols-2 gap-4 mb-4">
        {[
          { label: 'Silnik lewy', stats: leftStats, current: motorTemperatures.motorLeft },
          { label: 'Silnik prawy', stats: rightStats, current: motorTemperatures.motorRight }
        ].map(({ label, stats, current }, i) => (
          <div key={i} className="bg-gray-800 p-3 rounded-lg">
            <h4 className="text-gray-400 text-sm mb-1">{label}</h4>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-300">Aktualna:</span>
                <span className="font-mono text-white">{current.toFixed(1)}°C</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-300">Min:</span>
                <span className="font-mono text-white">{stats.min.toFixed(1)}°C</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-300">Max:</span>
                <span className="font-mono text-white">{stats.max.toFixed(1)}°C</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-300">Średnia:</span>
                <span className="font-mono text-white">{stats.avg.toFixed(1)}°C</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-2 gap-4">
        {Object.entries(motorTemperatures).map(([motorName, temperature]) => {
          const tempValue = typeof temperature === 'number' ? temperature : 0;
          const motorLabel = motorName.replace('motor', '');
          return (
            <div key={motorName} className="space-y-1">
              <div className="flex justify-between items-center">
                <span className="capitalize text-gray-300">{motorLabel}:</span>
                <div className="flex items-center">
                  <span className="font-mono mr-2 text-white">{tempValue.toFixed(1)}°C</span>
                  {tempValue > 70 && (
                    <span className="text-red-500">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </span>
                  )}
                </div>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2.5">
                <div
                  className={`h-2.5 rounded-full ${
                    tempValue > 70 ? 'bg-red-500' :
                    tempValue > 50 ? 'bg-yellow-500' : 'bg-green-500'
                  }`}
                  style={{ width: `${Math.min(100, tempValue)}%` }}
                />
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
