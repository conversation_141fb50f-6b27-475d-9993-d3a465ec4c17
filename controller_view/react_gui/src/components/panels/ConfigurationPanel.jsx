export default function ConfigurationPanel({
  config,
  onConfigChange,
  serverIP,
  isLoadingIP,
  onFetchServerIP
}) {
  return (
    <div className="p-4 grid grid-cols-1 md:grid-cols-2 gap-4">
      {/* Ustawienia podstawowe */}
      <div className="bg-gray-900 rounded-xl p-4 border border-gray-700">
        <h3 className="text-lg font-semibold mb-4">Ustawienia podstawowe</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-gray-400 mb-1">Nazwa robota</label>
            <input
              type="text"
              value={config.robotName}
              onChange={(e) => onConfigChange({ ...config, robotName: e.target.value })}
              className="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2"
            />
          </div>

          <div>
            <label className="block text-gray-400 mb-1">Ma<PERSON><PERSON>alna pr<PERSON>d<PERSON>ć</label>
            <select
              value={config.maxSpeed}
              onChange={(e) => onConfigChange({ ...config, maxSpeed: parseFloat(e.target.value) })}
              className="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2"
            >
              <option value="0.5">0.5 m/s (niska)</option>
              <option value="1.0">1.0 m/s (standardowa)</option>
              <option value="1.5">1.5 m/s (wysoka)</option>
              <option value="2.0">2.0 m/s (ekstremalna)</option>
            </select>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="safetyMode"
              checked={config.safetyMode}
              onChange={(e) => onConfigChange({ ...config, safetyMode: e.target.checked })}
              className="mr-2"
            />
            <label htmlFor="safetyMode" className="text-gray-400">Tryb bezpieczeństwa</label>
          </div>
        </div>
      </div>

      {/* Ustawienia sieciowe */}
      <div className="bg-gray-900 rounded-xl p-4 border border-gray-700">
        <h3 className="text-lg font-semibold mb-4">Ustawienia sieciowe</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-gray-400 mb-1">Adres IP serwera</label>
            <div className="flex items-center">
              <span className="font-mono bg-gray-800 px-3 py-2 rounded-lg">
                {isLoadingIP ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Ładowanie...
                  </span>
                ) : (
                  serverIP
                )}
              </span>
              <button
                onClick={onFetchServerIP}
                className="ml-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg"
                disabled={isLoadingIP}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
}