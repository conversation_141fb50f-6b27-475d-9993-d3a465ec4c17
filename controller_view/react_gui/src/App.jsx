import { useState, useEffect } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import Toolbar from './components/Toolbar';
import ControlPanel from './components/panels/ControlPanel';
import SensorsPanel from './components/panels/SensorsPanel';
import CameraPanel from './components/panels/CameraPanel';
import CalibrationPanel from './components/panels/CalibrationPanel';
import ConfigurationPanel from './components/panels/ConfigurationPanel';

function App() {
  const [activeTab, setActiveTab] = useState('control');
  const [linearSpeed, setLinearSpeed] = useState(0);
  const [angularSpeed, setAngularSpeed] = useState(0);
  const [heading, setHeading] = useState(45);
  const [isConnected, setIsConnected] = useState(true);
  const [battery, setBattery] = useState(20);
  const [config, setConfig] = useState({
    maxSpeed: 1.0,
    safetyMode: true,
  });
  const [calibration, setCalibration] = useState({
    imuProgress: 65,
    motorsProgress: 40,
    sensorsProgress: 80
  });
  const [serverIP, setServerIP] = useState('Ładowanie...');
  const [isLoadingIP, setIsLoadingIP] = useState(true);

  const imuData = {
    orientation: { x: 0.12, y: -0.23, z: 0.34, w: 0.89 },
    acceleration: { x: 0.05, y: -0.12, z: 9.81 },
    gyro: { x: 0.01, y: -0.02, z: 0.03 }
  };

  const fetchServerIP = async () => {
    try {
      setIsLoadingIP(true);
      const response = await fetch('http://localhost:3000/api/ip');
      const data = await response.json();

      if (data.ip) {
        const ipAddresses = data.ip;
        setServerIP(ipAddresses);
      }
      else if (data.serverIP) {
        setServerIP(data.serverIP);
      }
      else {
        setServerIP('Nie można pobrać IP');
      }
    } catch (error) {
      console.error('Błąd podczas pobierania IP:', error);
      setServerIP('Błąd połączenia');
    } finally {
      setIsLoadingIP(false);
    }
  };


  useEffect(() => {
    fetchServerIP();
    const ipIntervalId = setInterval(fetchServerIP, 60000);
    
    // Pobieraj temperatury co 5 sekund
    
    return () => {
      clearInterval(ipIntervalId);
    };
  }, []);

  const handleEmergencyStop = () => {
    setLinearSpeed(0);
    setAngularSpeed(0);
  };

  const toggleConnection = () => setIsConnected(!isConnected);

  return (
    <div className="min-h-screen bg-gray-950 text-white">
      <Toolbar 
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        isConnected={isConnected}
        battery={battery}
        onEmergencyStop={handleEmergencyStop}
      />

      <div className="p-4">
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            {activeTab === 'control' && (
              <ControlPanel 
                linearSpeed={linearSpeed}
                angularSpeed={angularSpeed}
                heading={heading}
                onLinearSpeedChange={setLinearSpeed}
                onAngularSpeedChange={setAngularSpeed}
                onHeadingChange={setHeading}
                onSetActiveTab={setActiveTab}
              />
            )}
            {activeTab === 'sensors' && (
              <SensorsPanel 
                imuData={imuData}
                heading={heading}
              />
            )}
            {activeTab === 'camera' && <CameraPanel />}
            {activeTab === 'calibration' && (
              <CalibrationPanel 
                calibration={calibration}
                onCalibrationChange={setCalibration}
              />
            )}
            {activeTab === 'configuration' && (
              <ConfigurationPanel 
                config={config}
                onConfigChange={setConfig}
                serverIP={serverIP}
                isLoadingIP={isLoadingIP}
                onFetchServerIP={fetchServerIP}
              />
            )}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
}

export default App;