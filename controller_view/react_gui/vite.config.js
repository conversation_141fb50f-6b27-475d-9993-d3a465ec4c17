import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tailwindcss from '@tailwindcss/vite'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), tailwindcss(),],
  proxy: {
    '/api': {
      target: 'http://localhost:3000', // adres twojego backendu
      changeOrigin: true,
      secure: false,
      rewrite: (path) => {
          console.log(path)
          return path
      }

    }
  }
})
