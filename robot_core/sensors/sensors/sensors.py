#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
from sensor_msgs.msg import MagneticField
import smbus2
import time
import math

class GY273Node(Node):
    def __init__(self):
        super().__init__('gy273_node')
        
        # Konfiguracja I2C dla HMC5883L
        self.i2c_bus = 1  # Dla RPi 2/3/4 użyj 1, dla RPi 1 użyj 0
        self.hmc5883l_addr = 0x1E  # Adres I2C czujnika
        self.bus = smbus2.SMBus(self.i2c_bus)
        
        # Inicjalizacja czujnika
        self._init_hmc5883l()
        
        # Publikator danych magnetycznych
        self.publisher = self.create_publisher(
            MagneticField, 
            'magnetic_field', 
            10
        )
        
        # Timer do odczytu danych
        self.timer = self.create_timer(0.1, self.read_data)  # 10 Hz
        
        self.get_logger().info("GY-273 (HMC5883L) Node started")

    def _init_hmc5883l(self):
        try:
            # Konfiguracja trybu ciągłego pomiaru
            self.bus.write_byte_data(self.hmc5883l_addr, 0x02, 0x00)
            
            # Ustawienie zakresu pomiarowego ±1.3 Ga (1090 LSB/Gauss)
            self.bus.write_byte_data(self.hmc5883l_addr, 0x01, 0x20)
            
            # Ustawienie szybkości próbkowania (15 Hz)
            self.bus.write_byte_data(self.hmc5883l_addr, 0x00, 0x10)
            
        except Exception as e:
            self.get_logger().error(f"Init error: {str(e)}")
            raise

    def read_data(self):
        try:
            # Odczyt danych (6 bajtów: X_L, X_H, Z_L, Z_H, Y_L, Y_H)
            data = self.bus.read_i2c_block_data(self.hmc5883l_addr, 0x03, 6)
            
            # Konwersja na wartości 16-bitowe
            x = self._convert_raw(data[0], data[1])
            z = self._convert_raw(data[2], data[3])
            y = self._convert_raw(data[4], data[5])
            
            # Konwersja do Tesli (1 Gauss = 0.0001 Tesla)
            x_tesla = x * 0.0001
            y_tesla = y * 0.0001
            z_tesla = z * 0.0001
            
            # Przygotowanie wiadomości ROS
            msg = MagneticField()
            msg.header.stamp = self.get_clock().now().to_msg()
            msg.header.frame_id = "gy273_link"
            msg.magnetic_field.x = x_tesla
            msg.magnetic_field.y = y_tesla
            msg.magnetic_field.z = z_tesla
            
            # Obliczenie azymutu (opcjonalne)
            azimuth = math.degrees(math.atan2(y, x))
            if azimuth < 0:
                azimuth += 360
                
            self.get_logger().info(
                f"Field: X={x_tesla:.6f}T, Y={y_tesla:.6f}T, Z={z_tesla:.6f}T | "
                f"Azimuth: {azimuth:.1f}°",
                throttle_duration_sec=1.0
            )
            
            self.publisher.publish(msg)
            
        except Exception as e:
            self.get_logger().error(f"Read error: {str(e)}")

    def _convert_raw(self, low, high):
        value = (high << 8) | low
        return value - 0x10000 if value > 0x7FFF else value

    def __del__(self):
        self.bus.close()

def main(args=None):
    rclpy.init(args=args)
    node = GY273Node()
    
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()