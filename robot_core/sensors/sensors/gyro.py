#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from std_msgs.msg import Float32MultiArray
import smbus2
import time
import struct
import math

class IMUPublisher(Node):
    def __init__(self):
        super().__init__('imu_publisher')
        
        # Inicjalizacja I2C
        self.bus = smbus2.SMBus(1)  # 1 dla Raspberry Pi
        self.accel_gyro_address = 0x69
        self.magnetometer_address = 0x1e  # Adres HMC5883L
        
        # Stałe do skalowania danych
        self.ACCEL_SCALE = 4.0 / 32768.0  # ±4g
        self.GYRO_SCALE = 500.0 / 32768.0  # ±500°/s
        self.MAG_SCALE = 0.92  # Skalowanie dla HMC5883L (mG na LSB)
        
        # Konfiguracja sensorów
        self._setup_icm20600()
        self._setup_hmc5883l()
        
        # Publisher dla danych
        self.publisher = self.create_publisher(Float32MultiArray, 'imu_data', 10)
        self.msg = Float32MultiArray()
        
        # Timer do odczytu danych
        self.timer = self.create_timer(0.1, self.read_and_publish)  # 10 Hz
        
        # Zmienne do kalibracji
        self.mag_calibration = (0, 0, 0)
        self.mag_scale = (1.0, 1.0, 1.0)
        
        self.get_logger().info("IMU Publisher (ICM20600 + HMC5883L) uruchomiony")

    def _setup_icm20600(self):
        try:
            # Reset i konfiguracja ICM20600
            self.bus.write_byte_data(self.accel_gyro_address, 0x6B, 0x80)
            time.sleep(0.1)
            self.bus.write_byte_data(self.accel_gyro_address, 0x6B, 0x00)
            time.sleep(0.01)
            self.bus.write_byte_data(self.accel_gyro_address, 0x1C, 0x08)  # ±4g
            self.bus.write_byte_data(self.accel_gyro_address, 0x1B, 0x08)  # ±500°/s
            self.bus.write_byte_data(self.accel_gyro_address, 0x1A, 0x06)  # DLPF
            time.sleep(0.1)
        except Exception as e:
            self.get_logger().error(f"Błąd konfiguracji ICM20600: {str(e)}")
            raise

    def _setup_hmc5883l(self):
        try:
            # Konfiguracja HMC5883L
            self.bus.write_byte_data(self.magnetometer_address, 0x00, 0x70)  # 8-average, 15 Hz
            self.bus.write_byte_data(self.magnetometer_address, 0x01, 0x20)  # ±1.3 Ga
            self.bus.write_byte_data(self.magnetometer_address, 0x02, 0x00)  # Continuous mode
            time.sleep(0.1)
        except Exception as e:
            self.get_logger().error(f"Błąd konfiguracji HMC5883L: {str(e)}")
            raise

    def read_icm20600(self):
        try:
            data = self.bus.read_i2c_block_data(self.accel_gyro_address, 0x3B, 14)
            if len(data) < 14:
                return None
                
            values = struct.unpack('>7h', bytes(data[:14]))
            return {
                'accel': {
                    'x': values[0] * self.ACCEL_SCALE,
                    'y': values[1] * self.ACCEL_SCALE,
                    'z': values[2] * self.ACCEL_SCALE
                },
                'gyro': {
                    'x': values[4] * self.GYRO_SCALE,
                    'y': values[5] * self.GYRO_SCALE,
                    'z': values[6] * self.GYRO_SCALE
                }
            }
        except Exception as e:
            self.get_logger().error(f"Błąd odczytu ICM20600: {str(e)}")
            return None

    def read_hmc5883l(self):
        try:
            data = self.bus.read_i2c_block_data(self.magnetometer_address, 0x03, 6)
            if len(data) < 6:
                return None
                
            x = struct.unpack('>h', bytes(data[0:2]))[0] * self.MAG_SCALE
            z = struct.unpack('>h', bytes(data[2:4]))[0] * self.MAG_SCALE
            y = struct.unpack('>h', bytes(data[4:6]))[0] * self.MAG_SCALE
            
            # Kalibracja
            x = (x - self.mag_calibration[0]) * self.mag_scale[0]
            y = (y - self.mag_calibration[1]) * self.mag_scale[1]
            z = (z - self.mag_calibration[2]) * self.mag_scale[2]
            
            return {
                'mag': {
                    'x': x,
                    'y': y,
                    'z': z
                },
                'heading': math.atan2(y, x) * (180 / math.pi) % 360
            }
        except Exception as e:
            self.get_logger().error(f"Błąd odczytu HMC5883L: {str(e)}")
            return None

    def read_and_publish(self):
        accel_gyro_data = self.read_icm20600()
        mag_data = self.read_hmc5883l()
        
        if accel_gyro_data and mag_data:
            self.msg.data = [
                accel_gyro_data['accel']['x'],
                accel_gyro_data['accel']['y'],
                accel_gyro_data['accel']['z'],
                accel_gyro_data['gyro']['x'],
                accel_gyro_data['gyro']['y'],
                accel_gyro_data['gyro']['z'],
                mag_data['mag']['x'],
                mag_data['mag']['y'],
                mag_data['mag']['z'],
                mag_data['heading']
            ]
            self.publisher.publish(self.msg)

def main(args=None):
    rclpy.init(args=args)
    try:
        node = IMUPublisher()
        rclpy.spin(node)
    except KeyboardInterrupt:
        node.get_logger().info("Zamykanie węzła...")
    except Exception as e:
        node.get_logger().error(f"Błąd: {str(e)}")
    finally:
        node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()