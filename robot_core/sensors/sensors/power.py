#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
from std_msgs.msg import Float32MultiArray
import smbus2
import time
from rclpy.qos import QoSProfile

class INA3221Node(Node):
    def __init__(self):
        super().__init__('power_monitor')
        
        # Konfiguracja QoS
        qos_profile = QoSProfile(depth=10)
        
        # Parametry (dostosuj do swojego układu!)
        self.shunt_resistance = 0.1  # Rezystancja bocznika w omach (CRITICAL!)
        self.current_lsb = 0.00004  # 40µV/LSB dla INA3221
        self.voltage_lsb = 0.00125  # 1.25mV/LSB dla napięcia bus
        
        # Inicjalizacja I2C
        self.i2c_bus = 1
        self.ina3221_addr = 0x40
        self.bus = smbus2.SMBus(self.i2c_bus)
        
        # Konfiguracja czujnika
        self._configure_sensor()
        
        # Publikator
        self.publisher = self.create_publisher(Float32MultiArray, 'power_data', qos_profile)
        self.timer = self.create_timer(1.0, self.update_measurements)
        
        self.get_logger().info("INA3221 Monitor started with:")
        self.get_logger().info(f"Shunt R: {self.shunt_resistance}Ω")
        self.get_logger().info(f"Current LSB: {self.current_lsb}V/bit")
        self.voltage_calibration = 0.857  # Domyślnie 1.0, zmień jeśli potrzebujesz
        self.voltage_lsb = 0.00125 

    def _configure_sensor(self):
        """Konfiguruje rejestry INA3221"""
        try:
            # Konfiguracja:
            # - Włącz wszystkie kanały
            # - Średnia z 128 próbek
            # - Czas konwersji 1.1ms
            config = 0x7127
            self._write_register(0x00, config)
            
            # Weryfikacja zapisu
            read_back = self._read_register(0x00)
            if read_back != config:
                self.get_logger().error(f"Config mismatch! Sent: {hex(config)}, Received: {hex(read_back)}")
            else:
                self.get_logger().info("Sensor configured successfully")
                
        except Exception as e:
            self.get_logger().error(f"Configuration failed: {str(e)}")
            raise

    def _write_register(self, reg, value):
        try:
            data = [(value >> 8) & 0xFF, value & 0xFF]
            self.bus.write_i2c_block_data(self.ina3221_addr, reg, data)
        except Exception as e:
            self.get_logger().error(f"Write error @ reg {hex(reg)}: {str(e)}")
            raise

    def _read_register(self, reg):
        try:
            data = self.bus.read_i2c_block_data(self.ina3221_addr, reg, 2)
            return (data[0] << 8) | data[1]
        except Exception as e:
            self.get_logger().error(f"Read error @ reg {hex(reg)}: {str(e)}")
            raise

    def _get_bus_voltage(self, channel):
        """Odczytuje i kalibruje napięcie zasilania"""
        raw = self._read_register(0x02 + (2 * (channel - 1)))
        voltage = raw * self.voltage_lsb
        calibrated_voltage = voltage * self.voltage_calibration
        return calibrated_voltage

    def _get_shunt_voltage(self, channel):
        """Odczytuje napięcie na boczniku (V)"""
        raw = self._read_register(0x01 + (2 * (channel - 1)))
        
        # Obsługa wartości ujemnych (2's complement)
        if raw > 32767:
            raw -= 65536
            
        return raw * self.current_lsb

    def _calculate_current(self, shunt_voltage):
        """Oblicza prąd (A) z prawa Ohma"""
        return shunt_voltage / self.shunt_resistance

    def update_measurements(self):
        msg = Float32MultiArray()
        
        try:
            channel = 1  # Wybierz kanał (1-3)
            
            # Pomiar
            bus_voltage = self._get_bus_voltage(channel)
            shunt_voltage = self._get_shunt_voltage(channel)
            current = self._calculate_current(shunt_voltage)
            
            # Wypełnij wiadomość Float32MultiArray
            msg.data = [bus_voltage, current]
            
            # Debug
            self.get_logger().info(
                f"Ch{channel}: "
                f"Bus={bus_voltage:.3f}V, "
                f"Shunt={shunt_voltage:.6f}V, "
                f"I={current:.3f}A",
                throttle_duration_sec=1.0
            )
            
            # Publikuj wiadomość
            self.publisher.publish(msg)
            
        except Exception as e:
            self.get_logger().error(f"Measurement failed: {str(e)}")

def main(args=None):
    rclpy.init(args=args)
    node = INA3221Node()
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()