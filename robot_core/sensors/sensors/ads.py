#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
from std_msgs.msg import Float32
import smbus2
import time
import math
import numpy as np

class NTCParameters:
    def __init__(self):
        self.v_ref = 3.3
        self.r_series = 10000
        self.ntc_r25 = 10000
        self.b_value = 3950
        self.adc_max = 255
        self.min_voltage = 0.1
        self.max_voltage = self.v_ref - 0.1

class NTCSensor:
    def __init__(self, channel, publisher, name, node, params: NTCParameters):
        self.channel = channel
        self.publisher = publisher
        self.name = name
        self.temp_history = []
        self.history_size = 5
        self.node = node

        # Parametry NTC
        self.params = params

    def read_adc(self, bus, addr):
        try:
            cmd = 0x84 | ((self.channel << 4) & 0x70)
            bus.write_byte(addr, cmd)
            time.sleep(0.01)
            raw = bus.read_byte(addr)

            voltage = (raw / self.params.adc_max) * self.params.v_ref
            if voltage < self.params.min_voltage or voltage > self.params.max_voltage:
                self.node.get_logger().warning(
                    f"[{self.name}] Napięcie poza zakresem: {voltage:.2f}V"
                )
                return None
            return raw
        except Exception as e:
            self.node.get_logger().error(f"[{self.name}] Błąd I2C: {str(e)}")
            return None

    def calculate_temp(self, adc_value):
        if adc_value is None:
            return float('nan')

        v_out = (adc_value / self.params.adc_max) * self.params.v_ref
        try:
            ntc_resistance = (self.params.r_series * v_out) / (self.params.v_ref - v_out)
        except ZeroDivisionError:
            return float('nan')

        try:
            steinhart = math.log(ntc_resistance / self.params.ntc_r25) / self.params.b_value
            steinhart += 1.0 / (25.0 + 273.15)
            temp_k = 1.0 / steinhart
            return temp_k - 273.15
        except ZeroDivisionError:
            return float('nan')

    def moving_average(self, new_value):
        if not math.isnan(new_value):
            self.temp_history.append(new_value)
            if len(self.temp_history) > self.history_size:
                self.temp_history.pop(0)
            return np.mean(self.temp_history)
        return float('nan')

class NTC10K_3V3Node(Node):
    def __init__(self):
        super().__init__('ntc10k_3v3_node')

        self.i2c_bus = 1
        self.ads7830_addr = 0x48
        self.bus = smbus2.SMBus(self.i2c_bus)

        ntc_params = NTCParameters()

        # Czujnik 1 na A0
        pub1 = self.create_publisher(Float32, 'temperature_1', 10)
        self.sensor1 = NTCSensor(channel=0, publisher=pub1, name="A0", node=self, params=ntc_params)

        # Czujnik 2 na A1
        pub2 = self.create_publisher(Float32, 'temperature_2', 10)
        self.sensor2 = NTCSensor(channel=1, publisher=pub2, name="A1", node=self, params=ntc_params)

        # Timer
        self.timer = self.create_timer(1.0, self.read_sensors)

        self.get_logger().info("NTC 10K Node (2 channels) started with 3.3V Vref")

    def read_sensors(self):
        try:
            adc1 = self.sensor1.read_adc(self.bus, self.ads7830_addr)
            temp1 = self.sensor1.calculate_temp(adc1)
            filtered1 = self.sensor1.moving_average(temp1)
            self.sensor1.publisher.publish(Float32(data=filtered1))

            adc2 = self.sensor2.read_adc(self.bus, self.ads7830_addr)
            temp2 = self.sensor2.calculate_temp(adc2)
            filtered2 = self.sensor2.moving_average(temp2)
            self.sensor2.publisher.publish(Float32(data=filtered2))

        except Exception as e:
            self.get_logger().error(f"Błąd podczas odczytu czujników: {str(e)}")

    def __del__(self):
        if hasattr(self, 'bus'):
            self.bus.close()

def main(args=None):
    rclpy.init(args=args)
    node = NTC10K_3V3Node()

    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
