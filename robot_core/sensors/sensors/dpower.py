#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
from std_msgs.msg import UInt8
import smbus2
from rclpy.qos import QoSProfile, QoSReliabilityPolicy, QoSHistoryPolicy

class DS1841Node(Node):
    def __init__(self):
        super().__init__('ds1841_controller')

        # QoS konfiguracja
        qos_profile = QoSProfile(
            reliability=QoSReliabilityPolicy.RELIABLE,
            history=QoSHistoryPolicy.KEEP_LAST,
            depth=10
        )

        # Parametry I2C
        self.i2c_bus = 1
        self.ds1841_addr = 0x28  # Adres I2C (upewnij się jaki masz!)

        # Inicjalizacja I2C
        try:
            self.bus = smbus2.SMBus(self.i2c_bus)
        except Exception as e:
            self.get_logger().error(f"I2C init failed: {str(e)}")
            raise

        # Subskrybent do ustawiania rezystancji (0–127)
        self.subscription = self.create_subscription(
            UInt8,
            'ds1841/wiper_value',
            self.set_wiper,
            qos_profile
        )

        self.get_logger().info("DS1841 controller started")

    def set_wiper(self, msg: UInt8):
        """ Ustawia wartość wiper (rezystancję) potencjometru """
        value = msg.data
        if value > 127:
            self.get_logger().warn(f"Wiper value {value} is out of range (0-127)")
            return

        try:
            self.bus.write_byte_data(self.ds1841_addr, 0x00, value)
            self.get_logger().info(f"Wiper set to {value}")
        except Exception as e:
            self.get_logger().error(f"Failed to write wiper value: {str(e)}")

    def __del__(self):
        try:
            self.bus.close()
        except:
            pass

def main(args=None):
    rclpy.init(args=args)
    try:
        node = DS1841Node()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        if 'node' in locals():
            node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
