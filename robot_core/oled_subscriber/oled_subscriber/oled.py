#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from std_msgs.msg import String, Float32
from luma.core.interface.serial import i2c
from luma.oled.device import sh1106
from luma.core.render import canvas
from PIL import ImageFont, ImageDraw
import time
import psutil
import datetime

class OLEDDisplayNode(Node):
    def __init__(self):
        super().__init__('oled_display_node')
        
        # Initialize variables
        self.temp_left = None
        self.temp_right = None
        self.last_temp_left_update = 0
        self.last_temp_right_update = 0
        
        # OLED initialization
        try:
            # Try both common addresses (0x3C and 0x3D)
            try:
                self.serial = i2c(port=1, address=0x3D)
                self.device = sh1106(self.serial, width=128, height=64)
            except:
                self.serial = i2c(port=1, address=0x3D)
                self.device = sh1106(self.serial, width=128, height=64)
            
            # Clear display first
            with canvas(self.device) as draw:
                draw.rectangle(self.device.bounding_box, outline="black", fill="black")
            
            # Load fonts with fallbacks
            try:
                self.large_font = ImageFont.truetype('DejaVuSans.ttf', 20)
                self.medium_font = ImageFont.truetype('DejaVuSans.ttf', 14)
            except:
                try:
                    self.large_font = ImageFont.load_default()
                    self.medium_font = ImageFont.load_default()
                except:
                    self.get_logger().error("Failed to load fonts")
                    raise
            
            # Show initialization message
            with canvas(self.device) as draw:
                draw.rectangle(self.device.bounding_box, outline="black", fill="black")
                draw.text((10, 10), "OLED INITIALIZED", font=self.medium_font, fill="white")
                draw.text((15, 30), "Waiting for data...", font=self.medium_font, fill="white")
            
            # Subscriptions
            self.temp_left_sub = self.create_subscription(
                Float32, '/ads/temperature_1', self.temp_left_callback, 10)
            self.temp_right_sub = self.create_subscription(
                Float32, '/ads/temperature_2', self.temp_right_callback, 10)
            self.subscription = self.create_subscription(
                String, 'oled_display', self.display_callback, 10)
            
            # Display rotation
            self.display_mode = 0
            self.display_modes_count = 3  # Simplified modes for testing
            self.display_timer = self.create_timer(3.0, self.update_display)
            
            self.get_logger().info("OLED Display Node initialized successfully")

        except Exception as e:
            self.get_logger().error(f"Display initialization failed: {str(e)}")
            raise

    def update_display(self):
        try:
            with canvas(self.device) as draw:
                draw.rectangle(self.device.bounding_box, outline="black", fill="black")
                
                if self.display_mode == 0:
                    # System info
                    cpu = psutil.cpu_percent()
                    mem = psutil.virtual_memory().percent
                    draw.text((5, 5), f"CPU: {cpu:.1f}%", font=self.medium_font, fill="white")
                    draw.text((5, 30), f"MEM: {mem:.1f}%", font=self.medium_font, fill="white")
                
                elif self.display_mode == 1:
                    # Temperature info
                    left = "N/A" if self.temp_left is None else f"{self.temp_left:.1f}°C"
                    right = "N/A" if self.temp_right is None else f"{self.temp_right:.1f}°C"
                    draw.text((5, 5), f"L: {left}", font=self.large_font, fill="white")
                    draw.text((5, 35), f"R: {right}", font=self.large_font, fill="white")
                
                elif self.display_mode == 2:
                    # Time and uptime
                    time_str = datetime.datetime.now().strftime('%H:%M:%S')
                    uptime = self.get_uptime()
                    draw.text((5, 5), time_str, font=self.large_font, fill="white")
                    draw.text((5, 35), f"Up: {uptime}", font=self.medium_font, fill="white")
                
                self.display_mode = (self.display_mode + 1) % self.display_modes_count
                
        except Exception as e:
            self.get_logger().error(f"Display update failed: {str(e)}")

    def get_uptime(self):
        try:
            uptime_seconds = time.time() - psutil.boot_time()
            hours = int(uptime_seconds // 3600)
            minutes = int((uptime_seconds % 3600) // 60)
            return f"{hours}h{minutes}m"
        except:
            return "N/A"

    def temp_left_callback(self, msg):
        self.temp_left = msg.data
        self.last_temp_left_update = time.time()

    def temp_right_callback(self, msg):
        self.temp_right = msg.data
        self.last_temp_right_update = time.time()

    def display_callback(self, msg):
        try:
            with canvas(self.device) as draw:
                draw.rectangle(self.device.bounding_box, outline="black", fill="black")
                draw.text((5, 20), msg.data[:20], font=self.large_font, fill="white")
        except Exception as e:
            self.get_logger().error(f"Message display failed: {str(e)}")

def main(args=None):
    rclpy.init(args=args)
    try:
        node = OLEDDisplayNode()
        rclpy.spin(node)
    except KeyboardInterrupt:
        node.get_logger().info("Shutting down...")
    except Exception as e:
        print(f"Error: {str(e)}")
    finally:
        if 'node' in locals():
            node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()