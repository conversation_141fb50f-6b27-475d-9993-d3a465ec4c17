import rclpy
from rclpy.node import Node
from interfaces.srv import Motors
from std_msgs.msg import Float32MultiArray
import RPi.GPIO as GPIO
import time
import sys
import atexit
import math

class MotorController(Node):
    def __init__(self):
        super().__init__('motor_controller')

        # Baner startowy
        banner = r"""
  ____        _        ____            _             _           
 |  _ \  ___ | |_ ___ / ___|___  _ __ | |_ _ __ ___ | | ___  ___ 
 | | | |/ _ \| __/ _ \ |   / _ \| '_ \| __| '__/ _ \| |/ _ \/ __|
 | |_| | (_) | ||  __/ |__| (_) | | | | |_| | | (_) | |  __/\__ \\
 |____/ \___/ \__\___|\____\___/|_| |_|\__|_|  \___/|_|\___||___/
        """
        print(banner)

        self.get_logger().info("Initializing Motor Controller with RPi.GPIO")

        # Definicje pinów
        self.FORWARD_PIN_1 = 19    # Lewy silnik przód
        self.REVERSE_PIN_1 = 13    # Lewy silnik tył
        self.FORWARD_PIN_2 = 12    # Prawy silnik przód
        self.REVERSE_PIN_2 = 18    # Prawy silnik tył

        # Częstotliwość PWM
        self.PWM_FREQ = 8000  # 8 kHz

        # Flaga kontrolna
        self._running = True
        self._pwm_initialized = False

        # Parametry sterowania
        self.deadzone = 0.1       # Martwa strefa dla joysticka
        self.max_speed = 100       # Maksymalna prędkość (0-100)
        self.turn_ratio = 0.7      # Współczynnik skrętu

        # Inicjalizacja GPIO
        try:
            GPIO.setmode(GPIO.BCM)
            GPIO.setwarnings(False)

            GPIO.setup(self.FORWARD_PIN_1, GPIO.OUT)
            GPIO.setup(self.REVERSE_PIN_1, GPIO.OUT)
            GPIO.setup(self.FORWARD_PIN_2, GPIO.OUT)
            GPIO.setup(self.REVERSE_PIN_2, GPIO.OUT)

            # Inicjalizacja PWM
            self.pwm_forward_1 = GPIO.PWM(self.FORWARD_PIN_1, self.PWM_FREQ)
            self.pwm_reverse_1 = GPIO.PWM(self.REVERSE_PIN_1, self.PWM_FREQ)
            self.pwm_forward_2 = GPIO.PWM(self.FORWARD_PIN_2, self.PWM_FREQ)
            self.pwm_reverse_2 = GPIO.PWM(self.REVERSE_PIN_2, self.PWM_FREQ)

            self.pwm_forward_1.start(0)
            self.pwm_reverse_1.start(0)
            self.pwm_forward_2.start(0)
            self.pwm_reverse_2.start(0)
            self._pwm_initialized = True

            self.get_logger().info(f"PWM initialized at {self.PWM_FREQ} Hz")
        except Exception as e:
            self.get_logger().error(f"GPIO initialization failed: {str(e)}")
            self.cleanup()
            raise

        # ROS2 Service
        self.srv = self.create_service(
            Motors,
            'set_motor',
            self.set_motor_callback
        )

        # ROS2 Subscriber dla joysticka (Float32MultiArray)
        self.joy_sub = self.create_subscription(
            Float32MultiArray,
            '/controller/joystick',
            self.joy_callback,
            10
        )

        # Rejestracja funkcji sprzątającej
        atexit.register(self.cleanup)

        # Sekwencja startowa
        self.startup_sequence()

    def startup_sequence(self):
        try:
            self.get_logger().info("Running startup motor test sequence...")
            test_speed = 45

            # Do przodu
            self.set_motors(test_speed, test_speed)
            time.sleep(0.3)

            # Stop
            self.stop_motors()
            time.sleep(0.2)

            # Do tyłu
            self.set_motors(-test_speed, -test_speed)
            time.sleep(0.3)

            # Stop
            self.stop_motors()
            time.sleep(0.2)

            # Skręt w lewo
            self.set_motors(-test_speed, test_speed)
            time.sleep(0.3)

            # Stop
            self.stop_motors()
            time.sleep(0.2)

            # Skręt w prawo
            self.set_motors(test_speed, -test_speed)
            time.sleep(0.3)

            # Stop
            self.stop_motors()

            self.get_logger().info("Startup test completed.")
        except Exception as e:
            self.get_logger().error(f"Startup sequence failed: {str(e)}")
            self.stop_motors()
            raise

    def set_motors(self, left_speed, right_speed):
        """Ustawia silniki z podanymi prędkościami (może być ujemna dla reverse)"""
        if not self._running:
            return
            
        # Najpierw zatrzymaj silniki
        self.stop_motors()
        time.sleep(0.001)  # Krótkie opóźnienie
        
        # Ogranicz zakres
        left_speed = max(-100, min(100, left_speed))
        right_speed = max(-100, min(100, right_speed))
        
        # Lewy silnik
        if left_speed > 0:
            self.pwm_forward_1.ChangeDutyCycle(abs(left_speed))
            self.pwm_reverse_1.ChangeDutyCycle(0)
        else:
            self.pwm_forward_1.ChangeDutyCycle(0)
            self.pwm_reverse_1.ChangeDutyCycle(abs(left_speed))
            
        # Prawy silnik
        if right_speed > 0:
            self.pwm_forward_2.ChangeDutyCycle(abs(right_speed))
            self.pwm_reverse_2.ChangeDutyCycle(0)
        else:
            self.pwm_forward_2.ChangeDutyCycle(0)
            self.pwm_reverse_2.ChangeDutyCycle(abs(right_speed))

    def stop_motors(self):
        """Zatrzymuje wszystkie silniki"""
        if not self._pwm_initialized:
            return
            
        try:
            self.pwm_forward_1.ChangeDutyCycle(0)
            self.pwm_reverse_1.ChangeDutyCycle(0)
            self.pwm_forward_2.ChangeDutyCycle(0)
            self.pwm_reverse_2.ChangeDutyCycle(0)
        except:
            pass

    def joy_callback(self, msg):
        """Callback dla wiadomości Float32MultiArray z joysticka"""
        if not self._running or len(msg.data) < 2:
            return
            
        try:
            # Zakładamy, że msg.data zawiera:
            # [0] - wartość osi Y (przód/tył)
            # [1] - wartość osi X (skręty)
            y_value = -msg.data[0]  # Odwrócenie osi Y
            x_value = msg.data[1]
            
            # Zastosuj deadzone
            if abs(y_value) < self.deadzone:
                y_value = 0.0
            if abs(x_value) < self.deadzone:
                x_value = 0.0
                
            # Jeśli obie wartości w deadzone - zatrzymaj silniki
            if y_value == 0.0 and x_value == 0.0:
                self.stop_motors()
                return
                
            # Oblicz prędkości bazowe
            base_speed = y_value * self.max_speed
            
            # Oblicz różnicę prędkości dla skrętu
            turn_adjustment = x_value * self.turn_ratio * self.max_speed
            
            # Oblicz prędkości dla każdej gąsienicy
            left_speed = base_speed - turn_adjustment
            right_speed = base_speed + turn_adjustment
            
            # Ogranicz prędkości do zakresu
            left_speed = max(-self.max_speed, min(self.max_speed, left_speed))
            right_speed = max(-self.max_speed, min(self.max_speed, right_speed))
            
            # Ustaw silniki
            self.set_motors(left_speed, right_speed)
            
            # Debugowanie
            self.get_logger().debug(f"Joystick values: Y={y_value:.2f}, X={x_value:.2f} | Motors: L={left_speed:.1f}, R={right_speed:.1f}")
            
        except Exception as e:
            self.get_logger().error(f"Joystick control error: {str(e)}")
            self.stop_motors()

    def set_motor_callback(self, request, response):
        if not self._running:
            response.success = False
            return response
            
        try:
            if not request.state:
                self.stop_motors()
                response.success = True
                return response

            # Konwersja na sterowanie różnicowe
            speed = request.speed
            if not request.isforward:
                speed = -speed
                
            self.set_motors(speed, speed)
            response.success = True
            return response

        except Exception as e:
            self.get_logger().error(f"Motor control error: {str(e)}")
            self.stop_motors()
            response.success = False
            return response

    def cleanup(self):
        """Bezpiecznie czyści zasoby GPIO"""
        if not self._running or not self._pwm_initialized:
            return
            
        self._running = False
        self.get_logger().info("Cleaning up GPIO resources...")
        
        # Zatrzymaj silniki
        self.stop_motors()
        
        # Zatrzymaj PWM
        try:
            self.pwm_forward_1.stop()
            self.pwm_reverse_1.stop()
            self.pwm_forward_2.stop()
            self.pwm_reverse_2.stop()
        except:
            pass
            
        # Wyczyść GPIO
        try:
            GPIO.cleanup()
        except:
            pass

        self._pwm_initialized = False
        self.get_logger().info("Cleanup completed")

def main():
    rclpy.init()
    node = None
    try:
        node = MotorController()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass  # Nie loguj błędu dla Ctrl+C
    except Exception as e:
        if node:
            node.get_logger().error(f"Fatal error: {str(e)}")
    finally:
        if node:
            try:
                node.destroy_node()
            except:
                pass
        try:
            rclpy.shutdown()
        except:
            pass

if __name__ == '__main__':
    main()